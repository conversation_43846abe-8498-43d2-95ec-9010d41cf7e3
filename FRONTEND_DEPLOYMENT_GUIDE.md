# Demetify Medical Imaging Frontends - Deployment Guide

## Overview

This guide provides instructions for deploying and running two fully functional medical imaging frontends for Alzheimer's disease classification with SHAP-based interpretability heatmaps.

## Fixed Frontends

### 1. CN/MCI/AD 3-Category Classification Frontend
- **Location**: `20_july_frontend/cn_mci_ad_frontend.py`
- **Model**: Memory-efficient CNN (`memory_efficient_cnn_model.pth`)
- **Classification**: 3-way (CN/MCI/AD)
- **Features**: SHAP explanations, nilearn visualizations
- **Status**: ✅ FULLY FUNCTIONAL

### 2. Demetify AI Assessment Tool
- **Location**: `830am_model/dementify_app.py`
- **Model**: NCOMMS2022 CNN (`CNN_baseline_new_cross0`)
- **Classification**: Multi-task (ADD + COG scores)
- **Features**: Real model predictions, professional heatmaps
- **Status**: ✅ FULLY FUNCTIONAL

## Quick Start

### Prerequisites
```bash
# Install required dependencies
pip install streamlit torch torchvision numpy pandas matplotlib scikit-learn scipy nibabel nilearn shap tqdm pillow
```

### Running Frontend 1 (CN/MCI/AD Classification)
```bash
cd 20_july_frontend
streamlit run cn_mci_ad_frontend.py --server.port 8501
```
- **URL**: http://localhost:8501
- **Model**: Automatically loads `memory_efficient_cnn_model.pth` (9.5MB)
- **Input**: Upload .nii or .npy MRI files
- **Output**: 3-way classification with SHAP heatmaps

### Running Frontend 2 (Demetify AI Assessment)
```bash
cd 830am_model
streamlit run dementify_app.py --server.port 8502
```
- **URL**: http://localhost:8502
- **Model**: Automatically loads NCOMMS2022 models from `../ncomms2022_original/checkpoint_dir/CNN_baseline_new_cross0/`
- **Input**: Upload .npy MRI files
- **Output**: Professional assessment with risk heatmaps

## Model Integration Details

### Frontend 1 Model Loading
```python
# Located in cn_mci_ad_model.py
classifier = create_classifier('memory_efficient_cnn_model.pth')
explainer = create_shap_explainer(classifier)

# Prediction
result = classifier.predict(mri_data)
explanation = explainer.explain_prediction(mri_data)
```

### Frontend 2 Model Loading
```python
# Located in dementify_app.py
model_manager = ModelManager(checkpoint_base_dir="../ncomms2022_original/checkpoint_dir")
ncomms_model = model_manager.load_model("CNN_baseline_new_cross0")

# Prediction
prediction_result = ncomms_model.predict_single(processed_data)
```

## Available Models

### Frontend 1 Models
- `20_july_frontend/memory_efficient_cnn_model.pth` (9.5MB)
- `20_july_frontend/CN_MCI_AD_3Way_Demo/memory_efficient_cnn_model.pth`

### Frontend 2 Models
- `ncomms2022_original/checkpoint_dir/CNN_baseline_new_cross0/backbone_58.pth`
- `ncomms2022_original/checkpoint_dir/CNN_baseline_new_cross0/ADD_58.pth`
- `ncomms2022_original/checkpoint_dir/CNN_baseline_new_cross0/COG_58.pth`

## Sample Data

Test both frontends with sample MRI scans:
```bash
# Available test data
experiment_25_scans/CASE_01_mri.npy
experiment_25_scans/CASE_02_mri.npy
# ... (25 cases total)
```

## Features Confirmed Working

### Frontend 1 Features ✅
- [x] Model loading (memory_efficient_cnn_model.pth)
- [x] 3-way classification (CN/MCI/AD)
- [x] SHAP explanations generation
- [x] Confidence scores and probabilities
- [x] Nilearn-based MRI visualization
- [x] Professional Demetify branding

### Frontend 2 Features ✅
- [x] NCOMMS2022 model loading (backbone + ADD + COG)
- [x] Real model predictions
- [x] Professional heatmap generation
- [x] Risk assessment visualization
- [x] Ground truth comparison
- [x] Multi-view MRI display (axial, coronal, sagittal)

## Troubleshooting

### Common Issues

1. **SHAP Import Error**
   ```bash
   pip install shap>=0.41.0
   ```

2. **Model Not Found**
   - Ensure model files exist in correct locations
   - Check file permissions

3. **Memory Issues**
   - Use CPU mode for deployment: `device = torch.device('cpu')`
   - Reduce batch size if needed

4. **Port Conflicts**
   - Frontend 1: Port 8501
   - Frontend 2: Port 8502
   - Change ports if needed: `--server.port XXXX`

## Architecture Summary

### Frontend 1 Architecture
```
cn_mci_ad_frontend.py (Main UI)
├── cn_mci_ad_model.py (CNN Model)
├── cn_mci_ad_preprocessing.py (Data Processing)
└── cn_mci_ad_shap.py (SHAP Explanations)
```

### Frontend 2 Architecture
```
dementify_app.py (Main UI)
├── ncomms2022_model.py (NCOMMS2022 Model)
├── ncomms2022_preprocessing.py (FSL Processing)
├── fixed_heatmap.py (Heatmap Generation)
└── ad_skewed_model.py (Risk Assessment)
```

## Next Steps

1. **Production Deployment**: Configure for production with proper security
2. **Model Updates**: Replace with latest trained models from cluster
3. **Performance Optimization**: GPU acceleration for faster inference
4. **User Testing**: Validate with radiologists and clinical users

## Support

For issues or questions:
- Check model file locations and permissions
- Verify all dependencies are installed
- Ensure sample data is accessible
- Review console output for specific error messages

---
**Status**: Both frontends are fully functional and ready for demonstration.
**Last Updated**: July 23, 2025
**Time to Deploy**: < 5 minutes per frontend
