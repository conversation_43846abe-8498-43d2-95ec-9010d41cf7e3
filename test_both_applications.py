#!/usr/bin/env python3
"""
Comprehensive test script for both Demetify frontend applications
Tests all core functionality including model loading, preprocessing, and heatmap generation
"""

import os
import sys
import numpy as np
import torch
import traceback
from pathlib import Path

def test_application(app_dir, app_name):
    """Test a single application"""
    print(f"\n{'='*60}")
    print(f"🧪 TESTING {app_name}")
    print(f"{'='*60}")
    
    # Change to application directory
    original_dir = os.getcwd()
    app_path = os.path.abspath(app_dir)
    os.chdir(app_path)
    sys.path.insert(0, app_path)

    try:
        # Import application modules
        print("📦 Importing modules...")
        import importlib.util
        spec = importlib.util.spec_from_file_location("app", os.path.join(app_path, "app.py"))
        app_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(app_module)

        load_model = app_module.load_model
        preprocess_mri = app_module.preprocess_mri
        create_shap_heatmap = app_module.create_shap_heatmap
        predict_classification = app_module.predict_classification
        print("✅ Successfully imported all modules")
        
        # Test model loading
        print("\n🔧 Testing model loading...")
        model, device = load_model()
        if model is not None and device is not None:
            print(f"✅ Model loaded successfully: {type(model).__name__}")
            print(f"✅ Device: {device}")
        else:
            print("❌ Model loading failed")
            return False
        
        # Test preprocessing
        print("\n🔄 Testing MRI preprocessing...")
        dummy_mri = np.random.randn(91, 109, 91) * 100 + 500
        processed = preprocess_mri(dummy_mri)
        if processed is not None:
            print(f"✅ Preprocessing successful: {processed.shape}")
            print(f"✅ Data type: {processed.dtype}")
            print(f"✅ Value range: [{processed.min():.3f}, {processed.max():.3f}]")
        else:
            print("❌ Preprocessing failed")
            return False
        
        # Test prediction
        print("\n🎯 Testing classification prediction...")
        probabilities = predict_classification(model, device, processed)
        if probabilities is not None:
            print(f"✅ Prediction successful: {probabilities.shape}")
            print(f"✅ Probabilities: {probabilities}")
            print(f"✅ Sum of probabilities: {probabilities.sum():.6f}")
            
            # Check if probabilities are valid
            if abs(probabilities.sum() - 1.0) < 0.001:
                print("✅ Probabilities sum to 1.0 (valid)")
            else:
                print("⚠️ Probabilities don't sum to 1.0")
        else:
            print("❌ Prediction failed")
            return False
        
        # Test heatmap generation for all classes
        print("\n🔥 Testing heatmap generation...")
        class_names = ['CN', 'MCI', 'AD']
        for class_idx in range(3):
            heatmap = create_shap_heatmap(model, device, processed, class_idx)
            if heatmap is not None:
                print(f"✅ {class_names[class_idx]} heatmap: {heatmap.shape}")
                print(f"   Range: [{heatmap.min():.6f}, {heatmap.max():.6f}]")
                print(f"   Non-zero values: {np.count_nonzero(heatmap)}/{heatmap.size}")
            else:
                print(f"❌ {class_names[class_idx]} heatmap generation failed")
                return False
        
        # Test edge cases
        print("\n🧪 Testing edge cases...")
        
        # Test with zero data
        zero_data = np.zeros((91, 109, 91))
        processed_zero = preprocess_mri(zero_data)
        if processed_zero is None:
            print("✅ Correctly rejected zero MRI data")
        else:
            print("⚠️ Zero MRI data was processed (may be acceptable)")
        
        # Test with invalid class index
        invalid_heatmap = create_shap_heatmap(model, device, processed, 5)
        if invalid_heatmap is None:
            print("✅ Correctly rejected invalid class index")
        else:
            print("❌ Invalid class index was accepted")
            return False
        
        print(f"\n🎉 {app_name} - ALL TESTS PASSED!")
        return True
        
    except Exception as e:
        print(f"\n❌ {app_name} - TEST FAILED with error:")
        print(f"Error: {str(e)}")
        traceback.print_exc()
        return False
    
    finally:
        # Restore original directory and path
        os.chdir(original_dir)
        if app_dir in sys.path:
            sys.path.remove(app_dir)

def test_deployment_scripts():
    """Test deployment scripts"""
    print(f"\n{'='*60}")
    print("🚀 TESTING DEPLOYMENT SCRIPTS")
    print(f"{'='*60}")
    
    apps = [
        ("attention_resnet_frontend", "Attention ResNet"),
        ("gated_attention_frontend", "Gated Attention CNN")
    ]
    
    for app_dir, app_name in apps:
        if os.path.exists(app_dir):
            print(f"\n📋 Checking {app_name} deployment files...")
            
            # Check required files
            required_files = ['app.py', 'requirements.txt', 'deploy.py', 'deploy.bat', 'README.md']
            for file in required_files:
                file_path = os.path.join(app_dir, file)
                if os.path.exists(file_path):
                    print(f"✅ {file} exists")
                else:
                    print(f"❌ {file} missing")
            
            # Check models directory
            models_dir = os.path.join(app_dir, 'models')
            if os.path.exists(models_dir):
                print(f"✅ models/ directory exists")
                model_files = os.listdir(models_dir)
                if model_files:
                    print(f"✅ Model files found: {model_files}")
                else:
                    print(f"⚠️ No model files in models/ directory")
            else:
                print(f"❌ models/ directory missing")

def main():
    """Main test function"""
    print("🧠 DEMETIFY FRONTEND APPLICATIONS - COMPREHENSIVE TEST SUITE")
    print("=" * 80)
    
    # Test both applications
    apps = [
        ("attention_resnet_frontend", "ATTENTION-GUIDED RESNET"),
        ("gated_attention_frontend", "GATED ATTENTION CNN")
    ]
    
    results = {}
    
    for app_dir, app_name in apps:
        if os.path.exists(app_dir):
            results[app_name] = test_application(app_dir, app_name)
        else:
            print(f"\n❌ {app_name} directory not found: {app_dir}")
            results[app_name] = False
    
    # Test deployment scripts
    test_deployment_scripts()
    
    # Final summary
    print(f"\n{'='*80}")
    print("📊 FINAL TEST RESULTS")
    print(f"{'='*80}")
    
    all_passed = True
    for app_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{app_name:30} {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 ALL APPLICATIONS PASSED COMPREHENSIVE TESTING!")
        print("🚀 Both frontends are ready for deployment and demonstration.")
        return 0
    else:
        print(f"\n❌ SOME TESTS FAILED!")
        print("🔧 Please review the errors above and fix the issues.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
