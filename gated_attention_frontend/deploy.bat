@echo off
REM Demetify Gated Attention CNN MRI Classifier - Windows Deployment Script
REM One-click deployment with automatic Python setup

echo.
echo ========================================
echo Demetify - Gated Attention CNN MRI Classifier
echo Windows Deployment Script
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
    goto :python_found
)

python3 --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python3
    goto :python_found
)

echo Python is not installed or not in PATH
echo Please install Python 3.8 or higher from https://python.org
pause
exit /b 1

:python_found
REM Display Python version
echo Checking Python installation...
%PYTHON_CMD% --version

REM Check if pip is available
%PYTHON_CMD% -m pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo pip is not available
    echo Please ensure pip is installed with Python
    pause
    exit /b 1
)

REM Install dependencies
echo.
echo Installing dependencies...
%PYTHON_CMD% -m pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo Failed to install dependencies
    pause
    exit /b 1
)

REM Check if model file exists
if not exist "models\gated_attention_cnn_final.pth" (
    echo Model file not found: models\gated_attention_cnn_final.pth
    echo Please ensure the model file is in the correct location
    pause
    exit /b 1
)

REM Launch the application
echo.
echo Launching Demetify Gated Attention CNN MRI Classifier...
echo Application will open in your default browser
echo Press Ctrl+C to stop the application
echo.

%PYTHON_CMD% -m streamlit run app.py --server.port 8502 --server.headless true --browser.gatherUsageStats false

pause
