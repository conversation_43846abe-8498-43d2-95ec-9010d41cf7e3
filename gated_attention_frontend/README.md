# Demetify - Gated Attention CNN MRI Classifier

Advanced AI-powered medical imaging analysis for radiologist assistance using Gated Attention CNN with Multi-Scale Feature Extraction.

## Overview

This application provides 3-way classification of brain MRI scans:
- **CN (Cognitive Normal)**: Healthy brain scans
- **MCI (Mild Cognitive Impairment)**: Early signs of cognitive decline
- **AD (Alzheimer's Disease)**: Advanced neurodegenerative changes

## Features

- 🧠 **Real-time MRI Classification** with gated attention mechanisms
- 🔥 **SHAP-based Interpretability Heatmaps** for model explainability
- 📁 **Multi-format Support**: .nii, .nii.gz, .npy files
- 🎯 **Proper Radiological Orientations** using nilearn
- 📊 **Interactive Visualizations** with fullscreen capability
- 🏥 **Clinical-grade Interface** designed for radiologists

## Model Architecture

- **Base Architecture**: Multi-Scale CNN with Gated Attention
- **Attention Mechanisms**: Channel, Spatial, and Gated Attention
- **Multi-Scale Features**: 1x1, 3x3, 5x5 convolutions + pooling
- **Training**: 300 epochs on NACC/ADNI datasets
- **Performance**: 68.0% validation accuracy
- **Model Size**: ~9MB

## Installation & Deployment

### Quick Start (Windows)
1. Double-click `deploy.bat` for one-click deployment
2. Application will automatically open in your browser

### Manual Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Launch application
python deploy.py
# or
streamlit run app.py
```

### Requirements
- Python 3.8 or higher
- 4GB+ RAM recommended
- Model file: `models/gated_attention_cnn_final.pth`

## Usage

1. **Upload MRI Scan**: Drag and drop or browse for .nii/.npy files
2. **View Classification**: See probability scores for CN/MCI/AD
3. **Generate Heatmaps**: Click tabs to create interpretability maps
4. **Analyze Results**: Review attention regions highlighted in red/yellow

## File Structure

```
gated_attention_frontend/
├── app.py                              # Main Streamlit application
├── deploy.py                           # Python deployment script
├── deploy.bat                          # Windows batch deployment
├── requirements.txt                    # Python dependencies
├── README.md                          # This file
├── models/
│   └── gated_attention_cnn_final.pth  # Trained model weights
└── gated_attention_cnn.py             # Original training script
```

## Technical Details

### Model Architecture
- **Input Shape**: (1, 1, 91, 109, 91)
- **Multi-Scale Blocks**: Parallel 1x1, 3x3, 5x5 convolutions
- **Gated Attention**: Learnable gating between channel and spatial attention
- **Global Attention**: Final attention pooling before classification
- **Output**: 3-class softmax probabilities

### Preprocessing Pipeline
- **Normalization**: Z-score normalization
- **Resizing**: Automatic resize to (91, 109, 91)
- **Format Detection**: Automatic .nii vs .npy handling
- **Brain2020 Methodology**: Skull stripping and brain extraction

### SHAP Heatmaps
- **Method**: Gradient-based attribution
- **Threshold**: >0.2% activation for clinical visibility
- **Overlay**: Red (high risk) and yellow (moderate risk) regions
- **Views**: Sagittal, Coronal, and Axial orientations

### Gated Attention Mechanism
- **Channel Gate**: Adaptive pooling + MLP for channel attention
- **Spatial Gate**: 1x1 convolution for spatial attention
- **Learnable Gating**: Trainable parameters to balance channel vs spatial
- **Dynamic Weighting**: Attention weights adapt based on input features

## Model Comparison

| Feature | Gated Attention CNN | Attention ResNet |
|---------|-------------------|------------------|
| Model Size | ~9MB | ~32MB |
| Validation Accuracy | 68.0% | 69.6% |
| Architecture | Multi-Scale + Gated | ResNet + Attention |
| Overfitting | Less severe | More severe |
| Training Stability | Higher | Lower |

## Team

- **University**: University of Illinois at Urbana-Champaign
- **Project Lead**: Prof. S. Seshadri
- **Research Team**: Medical AI Research Group
- **Application**: Radiologist assistance tool

## Disclaimer

This tool is designed to assist radiologists and should not replace professional medical judgment. All results should be interpreted by qualified medical professionals.

## Support

For technical support or questions about the model, please contact the Medical AI Research Team at UIUC.

---

**Demetify - Medical AI Research Platform**  
*University of Illinois at Urbana-Champaign*
