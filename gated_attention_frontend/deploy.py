#!/usr/bin/env python3
"""
Deployment script for Demetify Gated Attention CNN MRI Classifier
Handles environment setup, dependency installation, and application launch
"""

import os
import sys
import subprocess
import platform
import argparse

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    try:
        # Try pip install first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print("💡 You may need to install dependencies manually:")
        print("   pip install -r requirements.txt")
        return False

def check_model_file():
    """Check if model file exists"""
    model_path = "models/gated_attention_cnn_final.pth"
    if os.path.exists(model_path):
        print(f"✅ Model file found: {model_path}")
        return True
    else:
        print(f"❌ Model file not found: {model_path}")
        print("Please ensure the model file is in the correct location")
        return False

def launch_app(port=8502, host="localhost"):
    """Launch the Streamlit application"""
    print(f"🚀 Launching Demetify Gated Attention CNN MRI Classifier...")
    print(f"🌐 Application will be available at: http://{host}:{port}")
    print("💡 Press Ctrl+C to stop the application")

    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", str(port),
            "--server.address", host,
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Failed to launch application: {e}")
        print("💡 Try running manually: streamlit run app.py")

def main():
    """Main deployment function"""
    parser = argparse.ArgumentParser(description="Deploy Demetify Gated Attention CNN MRI Classifier")
    parser.add_argument("--port", type=int, default=8502, help="Port to run the application on")
    parser.add_argument("--host", default="localhost", help="Host to run the application on")
    parser.add_argument("--skip-install", action="store_true", help="Skip dependency installation")
    
    args = parser.parse_args()
    
    print("🧠 Demetify - Gated Attention CNN MRI Classifier Deployment")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not args.skip_install:
        if not install_dependencies():
            sys.exit(1)
    
    # Check model file
    if not check_model_file():
        sys.exit(1)
    
    # Launch application
    launch_app(args.port, args.host)

if __name__ == "__main__":
    main()
