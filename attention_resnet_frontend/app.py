"""
Demetify - Attention-Guided ResNet for Brain MRI Classification
Advanced AI-powered medical imaging analysis for radiologist assistance

University of Illinois at Urbana-Champaign
Project Lead: Prof. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>
Developed by: Medical AI Research Team

This application provides 3-way classification of brain MRI scans:
- CN (Cognitive Normal)
- MCI (Mild Cognitive Impairment) 
- AD (Alzheimer's Disease)

Features:
- Real-time MRI classification with attention mechanisms
- SHAP-based interpretability heatmaps
- Support for both .nii and .npy formats
- Proper radiological orientations using nilearn
"""

import streamlit as st
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import nibabel as nib
import pandas as pd
from PIL import Image
import io
import os
import base64
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns
from nilearn import plotting, image
from nilearn.plotting import plot_anat
import warnings
warnings.filterwarnings('ignore')

# Try to import SHAP with fallback
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
    st.warning("⚠️ SHAP not available. Heatmap generation will use gradient-based attribution instead.")
    print("Warning: SHAP not installed. Using gradient-based attribution for interpretability.")

# Configure Streamlit page
st.set_page_config(
    page_title="Demetify - Attention ResNet MRI Classifier",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for UIUC branding and medical interface
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #13294B 0%, #E84A27 100%);
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        text-align: center;
    }
    .main-header h1 {
        color: white;
        margin: 0;
        font-size: 2.5rem;
        font-weight: bold;
    }
    .main-header p {
        color: #FFD100;
        margin: 0.5rem 0 0 0;
        font-size: 1.1rem;
    }
    .team-info {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #13294B;
        margin-bottom: 2rem;
    }
    .classification-result {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin: 1rem 0;
    }
    .probability-bar {
        background: #e9ecef;
        border-radius: 20px;
        height: 30px;
        margin: 0.5rem 0;
        position: relative;
        overflow: hidden;
    }
    .probability-fill {
        height: 100%;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        transition: width 0.3s ease;
    }
    .cn-color { background: linear-gradient(90deg, #28a745, #20c997); }
    .mci-color { background: linear-gradient(90deg, #ffc107, #fd7e14); }
    .ad-color { background: linear-gradient(90deg, #dc3545, #e83e8c); }
    .upload-section {
        background: #f8f9fa;
        padding: 2rem;
        border-radius: 10px;
        border: 2px dashed #13294B;
        text-align: center;
        margin-bottom: 2rem;
    }
    .stButton > button {
        background: linear-gradient(90deg, #13294B, #E84A27);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(19, 41, 75, 0.3);
    }
</style>
""", unsafe_allow_html=True)

# Model Architecture Classes
class ChannelAttention(nn.Module):
    """Channel attention module for focusing on important feature channels"""
    def __init__(self, in_channels, reduction_ratio=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool3d(1)
        self.max_pool = nn.AdaptiveMaxPool3d(1)
        
        self.fc = nn.Sequential(
            nn.Conv3d(in_channels, in_channels // reduction_ratio, kernel_size=1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv3d(in_channels // reduction_ratio, in_channels, kernel_size=1, bias=False)
        )
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out)

class SpatialAttention(nn.Module):
    """Spatial attention module for focusing on important regions in the brain"""
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()
        padding = kernel_size // 2
        self.conv = nn.Conv3d(2, 1, kernel_size=kernel_size, padding=padding, bias=False)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        out = torch.cat([avg_out, max_out], dim=1)
        out = self.conv(out)
        return self.sigmoid(out)

class AttentionBlock(nn.Module):
    """Combined attention block with both channel and spatial attention"""
    def __init__(self, in_channels):
        super(AttentionBlock, self).__init__()
        self.channel_att = ChannelAttention(in_channels)
        self.spatial_att = SpatialAttention()
    
    def forward(self, x):
        x = x * self.channel_att(x)
        x = x * self.spatial_att(x)
        return x

class ResidualBlock(nn.Module):
    """Residual block with attention mechanism"""
    def __init__(self, in_channels, out_channels, stride=1):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv3d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm3d(out_channels)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv3d(out_channels, out_channels, kernel_size=3, padding=1, bias=False)
        self.bn2 = nn.BatchNorm3d(out_channels)
        
        self.attention = AttentionBlock(out_channels)
        
        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv3d(in_channels, out_channels, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm3d(out_channels)
            )
    
    def forward(self, x):
        residual = x
        
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        
        out = self.conv2(out)
        out = self.bn2(out)
        
        # Apply attention
        out = self.attention(out)
        
        out += self.shortcut(residual)
        out = self.relu(out)
        
        return out

class AttentionGuidedResNet(nn.Module):
    """Attention-Guided ResNet for 3D brain MRI classification"""
    def __init__(self, num_classes=3):
        super(AttentionGuidedResNet, self).__init__()
        
        # Initial layers
        self.conv1 = nn.Conv3d(1, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.bn1 = nn.BatchNorm3d(64)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool3d(kernel_size=3, stride=2, padding=1)
        
        # Residual blocks with attention
        self.layer1 = self._make_layer(64, 64, 2, stride=1)
        self.layer2 = self._make_layer(64, 128, 2, stride=2)
        self.layer3 = self._make_layer(128, 256, 2, stride=2)
        
        # Global attention pooling
        self.attention_pool = nn.Sequential(
            nn.Conv3d(256, 1, kernel_size=1),
            nn.Sigmoid()
        )
        
        # Classification head
        self.avgpool = nn.AdaptiveAvgPool3d(1)
        self.fc = nn.Linear(256, num_classes)
        
        # Initialize weights
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm3d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def _make_layer(self, in_channels, out_channels, num_blocks, stride):
        layers = []
        layers.append(ResidualBlock(in_channels, out_channels, stride))
        for _ in range(1, num_blocks):
            layers.append(ResidualBlock(out_channels, out_channels))
        return nn.Sequential(*layers)
    
    def forward(self, x):
        # Initial convolution
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)
        
        # Residual blocks with attention
        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        
        # Global attention weighting
        attention_weights = self.attention_pool(x)
        x = x * attention_weights
        
        # Classification
        x = self.avgpool(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)
        
        return x

# Preprocessing and Utility Functions
def load_mri_data(file_path_or_bytes, file_name=None):
    """Load MRI data from file path or uploaded bytes"""
    try:
        if isinstance(file_path_or_bytes, str):
            # File path
            if file_path_or_bytes.endswith('.nii') or file_path_or_bytes.endswith('.nii.gz'):
                nii_img = nib.load(file_path_or_bytes)
                mri_data = nii_img.get_fdata()
            else:
                mri_data = np.load(file_path_or_bytes, allow_pickle=True)
        else:
            # Uploaded bytes
            if file_name and (file_name.endswith('.nii') or file_name.endswith('.nii.gz')):
                # Save bytes to temporary file for nibabel
                import tempfile
                with tempfile.NamedTemporaryFile(suffix='.nii', delete=False) as tmp_file:
                    tmp_file.write(file_path_or_bytes.getvalue())
                    tmp_file.flush()
                    nii_img = nib.load(tmp_file.name)
                    mri_data = nii_img.get_fdata()
            else:
                # Assume numpy format
                mri_data = np.load(io.BytesIO(file_path_or_bytes.getvalue()), allow_pickle=True)

        return mri_data
    except Exception as e:
        st.error(f"Error loading MRI data: {str(e)}")
        return None

def preprocess_mri(mri_data):
    """Preprocess MRI data following brain2020 methodology"""
    try:
        # Validate input
        if mri_data is None:
            st.error("❌ No MRI data provided for preprocessing")
            return None

        if not isinstance(mri_data, np.ndarray):
            st.error("❌ MRI data must be a numpy array")
            return None

        # Ensure 3D data
        if len(mri_data.shape) == 4:
            mri_data = mri_data[:, :, :, 0]
        elif len(mri_data.shape) != 3:
            st.error(f"❌ Invalid MRI data shape: {mri_data.shape}. Expected 3D or 4D array.")
            return None

        # Check for reasonable data range
        if np.all(mri_data == 0):
            st.error("❌ MRI data appears to be empty (all zeros)")
            return None

        # Normalize to standard shape (91, 109, 91)
        target_shape = (91, 109, 91)

        # Simple resizing if shapes don't match
        if mri_data.shape != target_shape:
            try:
                from scipy.ndimage import zoom
                zoom_factors = [target_shape[i] / mri_data.shape[i] for i in range(3)]
                mri_data = zoom(mri_data, zoom_factors, order=1)
            except ImportError:
                st.error("❌ scipy is required for MRI preprocessing. Please install scipy.")
                return None
            except Exception as zoom_error:
                st.error(f"❌ Error resizing MRI data: {str(zoom_error)}")
                return None

        # Normalize intensity values
        mean_val = np.mean(mri_data)
        std_val = np.std(mri_data)

        if std_val == 0:
            st.warning("⚠️ MRI data has zero standard deviation. Using min-max normalization instead.")
            min_val, max_val = np.min(mri_data), np.max(mri_data)
            if max_val > min_val:
                mri_data = (mri_data - min_val) / (max_val - min_val)
            else:
                mri_data = np.zeros_like(mri_data)
        else:
            mri_data = (mri_data - mean_val) / (std_val + 1e-8)

        # Clip extreme values
        mri_data = np.clip(mri_data, -3, 3)

        # Add batch and channel dimensions
        mri_data = mri_data[np.newaxis, np.newaxis, ...]  # Shape: (1, 1, 91, 109, 91)

        return torch.FloatTensor(mri_data)
    except Exception as e:
        st.error(f"❌ Error preprocessing MRI data: {str(e)}")
        st.info("💡 Please check that your MRI file is valid and properly formatted.")
        return None

def create_mri_visualization(mri_data, slice_idx=None):
    """Create MRI visualization using nilearn with proper orientation"""
    try:
        # Remove batch and channel dimensions for visualization
        if len(mri_data.shape) == 5:
            mri_data = mri_data[0, 0]
        elif len(mri_data.shape) == 4:
            mri_data = mri_data[0]

        # Create a nibabel image for nilearn
        affine = np.eye(4)
        nii_img = nib.Nifti1Image(mri_data, affine)

        # Create figure with subplots
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))

        # Sagittal view (rotated 90 degrees left as requested)
        plotting.plot_anat(nii_img, axes=axes[0], display_mode='x',
                          cut_coords=1, annotate=False, draw_cross=False,
                          title="Sagittal View")

        # Coronal view
        plotting.plot_anat(nii_img, axes=axes[1], display_mode='y',
                          cut_coords=1, annotate=False, draw_cross=False,
                          title="Coronal View")

        # Axial view
        plotting.plot_anat(nii_img, axes=axes[2], display_mode='z',
                          cut_coords=1, annotate=False, draw_cross=False,
                          title="Axial View")

        plt.tight_layout()
        return fig
    except Exception as e:
        st.error(f"Error creating MRI visualization: {str(e)}")
        return None

@st.cache_resource
def load_model():
    """Load the trained Attention-Guided ResNet model"""
    try:
        device = torch.device("cpu")  # Use CPU for deployment
        model = AttentionGuidedResNet(num_classes=3)

        # Load the trained weights
        model_path = "models/attention_guided_resnet_final.pth"

        # Check if model file exists
        if not os.path.exists(model_path):
            st.error(f"❌ Model file not found: {model_path}")
            st.info("Please ensure the model file is in the correct location.")
            return None, None

        # Load model with proper error handling
        try:
            checkpoint = torch.load(model_path, map_location=device)
            model.load_state_dict(checkpoint)
            model.eval()
            st.success("✅ Attention-Guided ResNet model loaded successfully")
            return model, device
        except Exception as load_error:
            st.error(f"❌ Error loading model weights: {str(load_error)}")
            st.info("The model file may be corrupted or incompatible.")
            return None, None

    except Exception as e:
        st.error(f"❌ Error initializing model: {str(e)}")
        return None, None

def predict_classification(model, device, mri_data):
    """Perform classification prediction"""
    try:
        with torch.no_grad():
            mri_tensor = mri_data.to(device)
            outputs = model(mri_tensor)
            probabilities = F.softmax(outputs, dim=1)

        return probabilities.cpu().numpy()[0]
    except Exception as e:
        st.error(f"Error during prediction: {str(e)}")
        return None

def create_shap_heatmap(model, device, mri_data, class_idx):
    """Generate SHAP-based heatmap for interpretability"""
    try:
        # Validate inputs
        if model is None or device is None or mri_data is None:
            st.error("❌ Invalid inputs for heatmap generation")
            return None

        if class_idx not in [0, 1, 2]:
            st.error(f"❌ Invalid class index: {class_idx}. Must be 0, 1, or 2.")
            return None

        # Use gradient-based attribution (works without SHAP)
        mri_tensor = mri_data.clone().to(device)
        mri_tensor.requires_grad_(True)

        # Clear any existing gradients
        if mri_tensor.grad is not None:
            mri_tensor.grad.zero_()

        # Forward pass
        outputs = model(mri_tensor)

        # Ensure outputs have correct shape
        if outputs.shape[1] != 3:
            st.error(f"❌ Model output shape mismatch: expected 3 classes, got {outputs.shape[1]}")
            return None

        class_score = outputs[0, class_idx]

        # Backward pass to get gradients
        class_score.backward()

        # Get gradients as attribution
        gradients = mri_tensor.grad.abs()

        # Remove batch and channel dimensions
        heatmap = gradients[0, 0].cpu().numpy()

        # Normalize heatmap
        heatmap_min, heatmap_max = heatmap.min(), heatmap.max()
        if heatmap_max > heatmap_min:
            heatmap = (heatmap - heatmap_min) / (heatmap_max - heatmap_min)
        else:
            heatmap = np.zeros_like(heatmap)

        # Apply threshold for clinical visibility (>0.2% as requested)
        threshold = 0.002
        heatmap[heatmap < threshold] = 0

        return heatmap
    except Exception as e:
        st.error(f"❌ Error generating attention heatmap: {str(e)}")
        st.info("💡 This may be due to model compatibility issues or insufficient memory.")
        return None

def create_heatmap_overlay(mri_data, heatmap, title="Attention Heatmap"):
    """Create heatmap overlay on MRI scan"""
    try:
        # Remove batch and channel dimensions
        if len(mri_data.shape) == 5:
            mri_data = mri_data[0, 0]
        elif len(mri_data.shape) == 4:
            mri_data = mri_data[0]

        # Create figure
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))

        # Get middle slices
        mid_sag = mri_data.shape[0] // 2
        mid_cor = mri_data.shape[1] // 2
        mid_ax = mri_data.shape[2] // 2

        # Sagittal view
        axes[0].imshow(mri_data[mid_sag, :, :], cmap='gray', alpha=0.8)
        axes[0].imshow(heatmap[mid_sag, :, :], cmap='hot', alpha=0.6, vmin=0, vmax=1)
        axes[0].set_title("Sagittal View")
        axes[0].axis('off')

        # Coronal view
        axes[1].imshow(mri_data[:, mid_cor, :], cmap='gray', alpha=0.8)
        axes[1].imshow(heatmap[:, mid_cor, :], cmap='hot', alpha=0.6, vmin=0, vmax=1)
        axes[1].set_title("Coronal View")
        axes[1].axis('off')

        # Axial view
        axes[2].imshow(mri_data[:, :, mid_ax], cmap='gray', alpha=0.8)
        axes[2].imshow(heatmap[:, :, mid_ax], cmap='hot', alpha=0.6, vmin=0, vmax=1)
        axes[2].set_title("Axial View")
        axes[2].axis('off')

        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        return fig
    except Exception as e:
        st.error(f"Error creating heatmap overlay: {str(e)}")
        return None

# Main Streamlit Application
def main():
    """Main application interface"""

    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🧠 Demetify - Attention ResNet MRI Classifier</h1>
        <p>Advanced AI-powered medical imaging analysis for radiologist assistance</p>
    </div>
    """, unsafe_allow_html=True)

    # Team information
    st.markdown("""
    <div class="team-info">
        <strong>University of Illinois at Urbana-Champaign</strong><br>
        <strong>Project Lead:</strong> Prof. S. Seshadri<br>
        <strong>Research Team:</strong> Medical AI Research Group<br>
        <strong>Model:</strong> Attention-Guided ResNet with Spatial & Channel Attention
    </div>
    """, unsafe_allow_html=True)

    # Load model
    model, device = load_model()
    if model is None:
        st.error("Failed to load the trained model. Please check the model file.")
        return

    # File upload section
    st.markdown("""
    <div class="upload-section">
        <h3>📁 Upload Brain MRI Scan</h3>
        <p>Supported formats: .nii, .nii.gz, .npy</p>
    </div>
    """, unsafe_allow_html=True)

    uploaded_file = st.file_uploader(
        "Choose an MRI file",
        type=['nii', 'gz', 'npy'],
        help="Upload a brain MRI scan in NIfTI (.nii, .nii.gz) or NumPy (.npy) format"
    )

    if uploaded_file is not None:
        # Display file information
        st.success(f"✅ File uploaded: {uploaded_file.name} ({uploaded_file.size} bytes)")

        # Load and preprocess MRI data
        with st.spinner("Loading and preprocessing MRI data..."):
            mri_data = load_mri_data(uploaded_file, uploaded_file.name)

            if mri_data is not None:
                st.success("✅ MRI data loaded successfully")

                # Preprocess for model
                processed_data = preprocess_mri(mri_data)

                if processed_data is not None:
                    st.success("✅ MRI data preprocessed successfully")

                    # Create two columns for layout
                    col1, col2 = st.columns([1, 1])

                    with col1:
                        st.subheader("📊 MRI Visualization")

                        # Create MRI visualization
                        with st.spinner("Creating MRI visualization..."):
                            mri_fig = create_mri_visualization(processed_data)
                            if mri_fig:
                                st.pyplot(mri_fig, use_container_width=True)
                                plt.close(mri_fig)

                    with col2:
                        st.subheader("🎯 Classification Results")

                        # Perform prediction
                        with st.spinner("Analyzing MRI scan..."):
                            probabilities = predict_classification(model, device, processed_data)

                            if probabilities is not None:
                                # Class labels and colors
                                class_labels = ['CN (Cognitive Normal)', 'MCI (Mild Cognitive Impairment)', 'AD (Alzheimer\'s Disease)']
                                class_colors = ['cn-color', 'mci-color', 'ad-color']

                                # Sort probabilities in descending order
                                sorted_indices = np.argsort(probabilities)[::-1]

                                st.markdown('<div class="classification-result">', unsafe_allow_html=True)

                                for i, idx in enumerate(sorted_indices):
                                    prob = probabilities[idx]
                                    label = class_labels[idx]
                                    color = class_colors[idx]

                                    # Highlight top prediction
                                    if i == 0:
                                        st.markdown(f"**🏆 Top Prediction: {label}**")

                                    # Create probability bar
                                    st.markdown(f"""
                                    <div class="probability-bar">
                                        <div class="probability-fill {color}" style="width: {prob*100}%">
                                            {label}: {prob:.1%}
                                        </div>
                                    </div>
                                    """, unsafe_allow_html=True)

                                st.markdown('</div>', unsafe_allow_html=True)

                    # SHAP Heatmap Generation
                    st.subheader("🔥 Interpretability Heatmaps")
                    st.write("Generate SHAP-based attention heatmaps to understand model decisions")

                    # Create tabs for different class heatmaps
                    tab1, tab2, tab3 = st.tabs(["CN vs Others", "MCI vs Others", "AD vs Others"])

                    with tab1:
                        if st.button("Generate CN Heatmap", key="cn_heatmap"):
                            with st.spinner("Generating CN attention heatmap..."):
                                heatmap = create_shap_heatmap(model, device, processed_data, 0)
                                if heatmap is not None:
                                    heatmap_fig = create_heatmap_overlay(processed_data, heatmap,
                                                                       "CN (Cognitive Normal) Attention Heatmap")
                                    if heatmap_fig:
                                        st.pyplot(heatmap_fig, use_container_width=True)
                                        plt.close(heatmap_fig)

                    with tab2:
                        if st.button("Generate MCI Heatmap", key="mci_heatmap"):
                            with st.spinner("Generating MCI attention heatmap..."):
                                heatmap = create_shap_heatmap(model, device, processed_data, 1)
                                if heatmap is not None:
                                    heatmap_fig = create_heatmap_overlay(processed_data, heatmap,
                                                                       "MCI (Mild Cognitive Impairment) Attention Heatmap")
                                    if heatmap_fig:
                                        st.pyplot(heatmap_fig, use_container_width=True)
                                        plt.close(heatmap_fig)

                    with tab3:
                        if st.button("Generate AD Heatmap", key="ad_heatmap"):
                            with st.spinner("Generating AD attention heatmap..."):
                                heatmap = create_shap_heatmap(model, device, processed_data, 2)
                                if heatmap is not None:
                                    heatmap_fig = create_heatmap_overlay(processed_data, heatmap,
                                                                       "AD (Alzheimer's Disease) Attention Heatmap")
                                    if heatmap_fig:
                                        st.pyplot(heatmap_fig, use_container_width=True)
                                        plt.close(heatmap_fig)

    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; padding: 1rem;">
        <p><strong>Demetify - Medical AI Research Platform</strong></p>
        <p>University of Illinois at Urbana-Champaign | Prof. S. Seshadri</p>
        <p><em>This tool is designed to assist radiologists and should not replace professional medical judgment.</em></p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
