"""
Attention-Guided ResNet for Brain MRI Classification
Implements a ResNet architecture with spatial attention mechanisms
specifically designed for 3D brain MRI classification.

Based on research from:
- "ARM-Net: Attention-guided residual multiscale CNN for multiclass brain tumor classification"
- "Enhancing brain tumor classification through ensemble attention mechanisms"
"""

import os
import time
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import accuracy_score, confusion_matrix, classification_report
import logging
import nibabel as nib

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/attention_resnet_training.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("AttentionResNet")

# Constants
NUM_CLASSES = 3  # CN, MCI, AD
INPUT_SHAPE = (1, 91, 109, 91)  # Standard shape after preprocessing
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
EPOCHS = 300  # Train for 300 epochs as requested
BATCH_SIZE = 8
LEARNING_RATE = 0.0001
WEIGHT_DECAY = 1e-5

class ChannelAttention(nn.Module):
    """Channel attention module for focusing on important feature channels"""
    def __init__(self, in_channels, reduction_ratio=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool3d(1)
        self.max_pool = nn.AdaptiveMaxPool3d(1)
        
        self.fc = nn.Sequential(
            nn.Conv3d(in_channels, in_channels // reduction_ratio, kernel_size=1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv3d(in_channels // reduction_ratio, in_channels, kernel_size=1, bias=False)
        )
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out)

class SpatialAttention(nn.Module):
    """Spatial attention module for focusing on important regions in the brain"""
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()
        padding = kernel_size // 2
        self.conv = nn.Conv3d(2, 1, kernel_size=kernel_size, padding=padding, bias=False)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        out = torch.cat([avg_out, max_out], dim=1)
        out = self.conv(out)
        return self.sigmoid(out)

class AttentionBlock(nn.Module):
    """Combined attention block with both channel and spatial attention"""
    def __init__(self, in_channels):
        super(AttentionBlock, self).__init__()
        self.channel_att = ChannelAttention(in_channels)
        self.spatial_att = SpatialAttention()
    
    def forward(self, x):
        x = x * self.channel_att(x)
        x = x * self.spatial_att(x)
        return x

class ResidualBlock(nn.Module):
    """Residual block with attention mechanism"""
    def __init__(self, in_channels, out_channels, stride=1):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv3d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm3d(out_channels)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv3d(out_channels, out_channels, kernel_size=3, padding=1, bias=False)
        self.bn2 = nn.BatchNorm3d(out_channels)
        
        self.attention = AttentionBlock(out_channels)
        
        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv3d(in_channels, out_channels, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm3d(out_channels)
            )
    
    def forward(self, x):
        residual = x
        
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        
        out = self.conv2(out)
        out = self.bn2(out)
        
        # Apply attention
        out = self.attention(out)
        
        out += self.shortcut(residual)
        out = self.relu(out)
        
        return out

class AttentionGuidedResNet(nn.Module):
    """Attention-Guided ResNet for 3D brain MRI classification"""
    def __init__(self, num_classes=NUM_CLASSES):
        super(AttentionGuidedResNet, self).__init__()
        
        # Initial layers
        self.conv1 = nn.Conv3d(1, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.bn1 = nn.BatchNorm3d(64)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool3d(kernel_size=3, stride=2, padding=1)
        
        # Residual blocks with attention
        self.layer1 = self._make_layer(64, 64, 2, stride=1)
        self.layer2 = self._make_layer(64, 128, 2, stride=2)
        self.layer3 = self._make_layer(128, 256, 2, stride=2)
        
        # Global attention pooling
        self.attention_pool = nn.Sequential(
            nn.Conv3d(256, 1, kernel_size=1),
            nn.Sigmoid()
        )
        
        # Classification head
        self.avgpool = nn.AdaptiveAvgPool3d(1)
        self.fc = nn.Linear(256, num_classes)
        
        # Initialize weights
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm3d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def _make_layer(self, in_channels, out_channels, num_blocks, stride):
        layers = []
        layers.append(ResidualBlock(in_channels, out_channels, stride))
        for _ in range(1, num_blocks):
            layers.append(ResidualBlock(out_channels, out_channels))
        return nn.Sequential(*layers)
    
    def forward(self, x):
        # Initial convolution
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)
        
        # Residual blocks with attention
        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        
        # Global attention weighting
        attention_weights = self.attention_pool(x)
        x = x * attention_weights
        
        # Classification
        x = self.avgpool(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)
        
        return x

class BrainMRIDataset(Dataset):
    """Dataset for loading preprocessed brain MRI data"""
    def __init__(self, csv_file, root_dir=None):
        self.data_frame = pd.read_csv(csv_file)
        self.root_dir = root_dir
        
        # Map class labels to indices
        self.class_map = {'CN': 0, 'MCI': 1, 'AD': 2}
        
        logger.info(f"Loaded dataset with {len(self.data_frame)} samples")
        logger.info(f"Class distribution: {self.data_frame['label'].value_counts()}")
    
    def __len__(self):
        return len(self.data_frame)
    
    def __getitem__(self, idx):
        if torch.is_tensor(idx):
            idx = idx.tolist()
        
        # Get file path and load the preprocessed MRI data
        file_path = self.data_frame.iloc[idx]['preprocessed_path']
        # Load NIfTI file properly
        if file_path.endswith('.nii') or file_path.endswith('.nii.gz'):
            # Load NIfTI file using nibabel
            nii_img = nib.load(file_path)
            mri_data = nii_img.get_fdata()
        else:
            # Load numpy file
            mri_data = np.load(file_path, allow_pickle=True)
        
        # Ensure data is in the right format (add channel dimension if needed)
        if len(mri_data.shape) == 3:
            mri_data = np.expand_dims(mri_data, axis=0)
        
        # Convert to tensor
        mri_tensor = torch.from_numpy(mri_data).float()
        
        # Get label
        diagnosis = self.data_frame.iloc[idx]['label']
        label = self.class_map[diagnosis]
        
        return mri_tensor, label

def train_model(model, train_loader, val_loader, criterion, optimizer, scheduler, num_epochs=EPOCHS):
    """Train the model and validate"""
    best_val_acc = 0.0
    best_model_wts = None
    
    for epoch in range(num_epochs):
        start_time = time.time()
        
        # Training phase
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for inputs, labels in train_loader:
            inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
            
            # Zero the parameter gradients
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            
            # Backward pass and optimize
            loss.backward()
            optimizer.step()
            
            # Statistics
            train_loss += loss.item() * inputs.size(0)
            _, predicted = torch.max(outputs, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
        
        train_loss = train_loss / len(train_loader.dataset)
        train_acc = train_correct / train_total
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        val_preds = []
        val_labels = []
        
        with torch.no_grad():
            for inputs, labels in val_loader:
                inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
                
                outputs = model(inputs)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item() * inputs.size(0)
                _, predicted = torch.max(outputs, 1)
                
                val_preds.extend(predicted.cpu().numpy())
                val_labels.extend(labels.cpu().numpy())
        
        val_loss = val_loss / len(val_loader.dataset)
        val_acc = accuracy_score(val_labels, val_preds)
        
        # Update learning rate
        scheduler.step(val_loss)
        
        # Save best model
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_model_wts = model.state_dict().copy()
            torch.save(model.state_dict(), 'attention_guided_resnet_model.pth')
            
            # Detailed validation metrics
            logger.info(f"\nValidation Classification Report (Epoch {epoch+1}):")
            logger.info(classification_report(val_labels, val_preds, target_names=['CN', 'MCI', 'AD']))
            
            conf_matrix = confusion_matrix(val_labels, val_preds)
            logger.info(f"Confusion Matrix:\n{conf_matrix}")
        
        epoch_time = time.time() - start_time
        
        # Log progress
        logger.info(f"Epoch {epoch+1}/{num_epochs} - Time: {epoch_time:.2f}s - "
                   f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}, "
                   f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
        
        # Save checkpoint every 10 epochs
        if (epoch + 1) % 10 == 0:
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'best_val_acc': best_val_acc
            }, f'checkpoints/attention_resnet_epoch_{epoch+1}.pth')
    
    # Load best model weights
    model.load_state_dict(best_model_wts)
    return model

def main():
    """Main function to run the training"""
    logger.info(f"Using device: {DEVICE}")
    logger.info(f"Starting training with {EPOCHS} epochs")
    
    # Create checkpoint directory
    os.makedirs('checkpoints', exist_ok=True)
    
    # Initialize model
    model = AttentionGuidedResNet(num_classes=NUM_CLASSES)
    model = model.to(DEVICE)
    logger.info(f"Model initialized: {model.__class__.__name__}")
    
    # Loss function and optimizer
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE, weight_decay=WEIGHT_DECAY)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode="min", factor=0.5, patience=10)
    
    # Load datasets
    train_dataset = BrainMRIDataset(
        csv_file='fold_0_train.csv',
        root_dir='preprocessed_data'
    )
    
    val_dataset = BrainMRIDataset(
        csv_file='fold_0_val.csv',
        root_dir='preprocessed_data'
    )
    
    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=4)
    
    logger.info(f"Training samples: {len(train_dataset)}")
    logger.info(f"Validation samples: {len(val_dataset)}")
    
    # Train the model
    trained_model = train_model(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        criterion=criterion,
        optimizer=optimizer,
        scheduler=scheduler,
        num_epochs=EPOCHS
    )
    
    # Save the final model
    torch.save(trained_model.state_dict(), 'attention_guided_resnet_final.pth')
    logger.info("Training completed and model saved!")

if __name__ == "__main__":
    main()
