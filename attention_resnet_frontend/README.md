# Demetify - Attention-Guided ResNet MRI Classifier

Advanced AI-powered medical imaging analysis for radiologist assistance using Attention-Guided ResNet architecture.

## Overview

This application provides 3-way classification of brain MRI scans:
- **CN (Cognitive Normal)**: Healthy brain scans
- **MCI (Mild Cognitive Impairment)**: Early signs of cognitive decline
- **AD (Alzheimer's Disease)**: Advanced neurodegenerative changes

## Features

- 🧠 **Real-time MRI Classification** with attention mechanisms
- 🔥 **SHAP-based Interpretability Heatmaps** for model explainability
- 📁 **Multi-format Support**: .nii, .nii.gz, .npy files
- 🎯 **Proper Radiological Orientations** using nilearn
- 📊 **Interactive Visualizations** with fullscreen capability
- 🏥 **Clinical-grade Interface** designed for radiologists

## Model Architecture

- **Base Architecture**: ResNet with 3D convolutions
- **Attention Mechanisms**: Channel and Spatial Attention blocks
- **Training**: 300 epochs on NACC/ADNI datasets
- **Performance**: 69.6% validation accuracy
- **Model Size**: ~32MB

## Installation & Deployment

### Quick Start (Windows)
1. Double-click `deploy.bat` for one-click deployment
2. Application will automatically open in your browser

### Manual Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Launch application
python deploy.py
# or
streamlit run app.py
```

### Requirements
- Python 3.8 or higher
- 4GB+ RAM recommended
- Model file: `models/attention_guided_resnet_final.pth`

## Usage

1. **Upload MRI Scan**: Drag and drop or browse for .nii/.npy files
2. **View Classification**: See probability scores for CN/MCI/AD
3. **Generate Heatmaps**: Click tabs to create interpretability maps
4. **Analyze Results**: Review attention regions highlighted in red/yellow

## File Structure

```
attention_resnet_frontend/
├── app.py                              # Main Streamlit application
├── deploy.py                           # Python deployment script
├── deploy.bat                          # Windows batch deployment
├── requirements.txt                    # Python dependencies
├── README.md                          # This file
├── models/
│   └── attention_guided_resnet_final.pth  # Trained model weights
└── attention_guided_resnet.py         # Original training script
```

## Technical Details

### Model Architecture
- **Input Shape**: (1, 1, 91, 109, 91)
- **Attention Blocks**: Channel + Spatial attention at each residual block
- **Global Attention**: Final attention pooling before classification
- **Output**: 3-class softmax probabilities

### Preprocessing Pipeline
- **Normalization**: Z-score normalization
- **Resizing**: Automatic resize to (91, 109, 91)
- **Format Detection**: Automatic .nii vs .npy handling
- **Brain2020 Methodology**: Skull stripping and brain extraction

### SHAP Heatmaps
- **Method**: Gradient-based attribution
- **Threshold**: >0.2% activation for clinical visibility
- **Overlay**: Red (high risk) and yellow (moderate risk) regions
- **Views**: Sagittal, Coronal, and Axial orientations

## Team

- **University**: University of Illinois at Urbana-Champaign
- **Project Lead**: Prof. S. Seshadri
- **Research Team**: Medical AI Research Group
- **Application**: Radiologist assistance tool

## Disclaimer

This tool is designed to assist radiologists and should not replace professional medical judgment. All results should be interpreted by qualified medical professionals.

## Support

For technical support or questions about the model, please contact the Medical AI Research Team at UIUC.

---

**Demetify - Medical AI Research Platform**  
*University of Illinois at Urbana-Champaign*
