"""
SHAP Interpretability Component for CN/MCI/AD 3-Category Classification
Generates gradient-based explanations and heatmaps for MRI scans
"""

import numpy as np
import torch
import torch.nn as nn
from typing import Union, Tuple, Optional, Dict
import logging
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors

# Try to import SHAP
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False

# Import model components
from cn_mci_ad_model import CNMCIADClassifier

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CNMCIADSHAPExplainer:
    """
    SHAP-based interpretability for CN/MCI/AD 3-category classification
    """
    
    def __init__(self, 
                 classifier: CNMCIADClassifier,
                 background_samples: Optional[np.ndarray] = None,
                 num_background: int = 10):
        """
        Initialize SHAP explainer
        
        Args:
            classifier: Trained CNMCIADClassifier instance
            background_samples: Background samples for SHAP. If None, generates random samples
            num_background: Number of background samples to use
        """
        self.classifier = classifier
        self.device = classifier.device
        self.num_background = num_background

        # Handle both list and dict formats for class_names
        if isinstance(classifier.class_names, list):
            self.class_labels = classifier.class_names
            self.class_names = classifier.class_names
        else:
            # If it's a dict, extract the keys
            self.class_labels = list(classifier.class_names.keys())
            self.class_names = list(classifier.class_names.keys())
        
        if not SHAP_AVAILABLE:
            logger.warning("SHAP not available. Using gradient-based explanations.")
            self.use_shap = False
        else:
            logger.info(f"SHAP available, version: {shap.__version__}")
            self.use_shap = True
        
        # Initialize explainers
        self.explainer = None
        self.use_gradients = not self.use_shap
        
        logger.info("CN/MCI/AD SHAP explainer initialized successfully")
    
    def _gradient_explanation(self, mri_data: np.ndarray, target_class: int = None) -> np.ndarray:
        """
        Generate gradient-based explanation as fallback
        
        Args:
            mri_data: Input MRI data
            target_class: Target class index (0=CN, 1=MCI, 2=AD). If None, uses predicted class
            
        Returns:
            Gradient-based saliency map
        """
        try:
            # Preprocess input and convert to tensor
            processed_data = self.classifier.preprocessor.preprocess_mri(mri_data)
            input_tensor = torch.from_numpy(processed_data).float()
            input_tensor = input_tensor.unsqueeze(0).unsqueeze(0)  # Add batch and channel dims
            input_tensor = input_tensor.to(self.device)
            input_tensor.requires_grad_(True)
            
            # Forward pass
            with torch.enable_grad():
                outputs = self.classifier.model(input_tensor)
                probabilities = torch.softmax(outputs, dim=1)
                
                # Use predicted class if target_class not specified
                if target_class is None:
                    target_class = torch.argmax(probabilities, dim=1).item()
                
                # Get target probability
                target_prob = probabilities[0, target_class]
                
                # Backward pass
                target_prob.backward()
                
                # Get gradients
                gradients = input_tensor.grad
                
                if gradients is not None:
                    # Convert to numpy and remove batch/channel dimensions
                    saliency = gradients.squeeze().cpu().numpy()
                    
                    # Take absolute value for importance
                    saliency = np.abs(saliency)
                    
                    # Apply smoothing
                    from scipy.ndimage import gaussian_filter
                    saliency = gaussian_filter(saliency, sigma=0.5)
                    
                    return saliency
                else:
                    logger.warning("No gradients computed, returning zero saliency")
                    return np.zeros_like(mri_data)
        
        except Exception as e:
            logger.error(f"Error generating gradient explanation: {e}")
            # Return a simple intensity-based saliency as ultimate fallback
            return np.abs(mri_data - np.mean(mri_data))
    
    def explain_prediction(self, mri_data: np.ndarray, target_class: int = None) -> Dict:
        """
        Generate explanation for prediction
        
        Args:
            mri_data: Input MRI data
            target_class: Target class to explain (0=CN, 1=MCI, 2=AD)
            
        Returns:
            Dictionary with explanation results
        """
        logger.info(f"Generating explanation for {self.class_names[target_class] if target_class is not None else 'predicted'} class...")
        
        # Get prediction first
        prediction_results = self.classifier.predict(mri_data)
        predicted_class_idx = self.class_names.index(prediction_results['predicted_class'])
        
        if target_class is None:
            target_class = predicted_class_idx
        
        # Generate explanation
        saliency = self._gradient_explanation(mri_data, target_class)
        
        return {
            'saliency_map': saliency,
            'target_class': self.class_names[target_class],
            'target_class_idx': target_class,
            'prediction_results': prediction_results
        }
    
    def create_heatmap_overlay(self,
                              mri_data: np.ndarray,
                              saliency_map: np.ndarray,
                              threshold_percentile: float = 95.0,
                              colormap: str = 'hot') -> Tuple[np.ndarray, np.ndarray]:
        """
        Create heatmap overlay for visualization
        
        Args:
            mri_data: Original MRI data
            saliency_map: Saliency/importance map
            threshold_percentile: Percentile threshold for heatmap
            colormap: Colormap for heatmap
            
        Returns:
            Tuple of (heatmap_mask, overlay_image)
        """
        try:
            # Ensure same shape
            if mri_data.shape != saliency_map.shape:
                logger.warning(f"Shape mismatch: MRI {mri_data.shape} vs Saliency {saliency_map.shape}")
                # Resize saliency map to match MRI
                from scipy.ndimage import zoom
                zoom_factors = [mri_data.shape[i] / saliency_map.shape[i] for i in range(3)]
                saliency_map = zoom(saliency_map, zoom_factors, order=1)
            
            # Create heatmap mask
            saliency_abs = np.abs(saliency_map)
            if np.max(saliency_abs) > 0:
                threshold = np.percentile(saliency_abs, threshold_percentile)
                heatmap_mask = saliency_abs >= threshold
                
                # Create normalized heatmap
                heatmap_normalized = saliency_abs / np.max(saliency_abs)
                heatmap_normalized = heatmap_normalized * heatmap_mask
            else:
                logger.warning("All saliency values are zero")
                heatmap_mask = np.zeros_like(saliency_abs, dtype=bool)
                heatmap_normalized = np.zeros_like(saliency_abs)
            
            # Create overlay (simple RGB stack)
            overlay = np.stack([heatmap_normalized] * 3, axis=-1)
            
            return heatmap_mask, overlay
        
        except Exception as e:
            logger.error(f"Error creating heatmap overlay: {e}")
            # Return fallback empty overlays
            return np.zeros_like(mri_data, dtype=bool), np.stack([mri_data] * 3, axis=-1)
    
    def generate_slice_visualizations(self, 
                                    mri_data: np.ndarray, 
                                    saliency_map: np.ndarray,
                                    slice_indices: Optional[Dict[str, int]] = None) -> Dict:
        """
        Generate slice visualizations for different anatomical views
        
        Args:
            mri_data: Original MRI data
            saliency_map: Saliency map
            slice_indices: Dictionary with slice indices for each view
            
        Returns:
            Dictionary with visualization data for each view
        """
        # Ensure both MRI data and saliency map have the same shape
        if mri_data.shape != saliency_map.shape:
            logger.info(f"Resizing MRI data from {mri_data.shape} to match saliency shape {saliency_map.shape}")
            from scipy.ndimage import zoom
            zoom_factors = [saliency_map.shape[i] / mri_data.shape[i] for i in range(3)]
            mri_data = zoom(mri_data, zoom_factors, order=1)

        if slice_indices is None:
            slice_indices = {
                'axial': mri_data.shape[2] // 2,
                'sagittal': mri_data.shape[0] // 2,
                'coronal': mri_data.shape[1] // 2
            }
        
        visualizations = {}
        
        try:
            logger.info("Creating heatmap overlay...")
            # Create heatmap overlay
            heatmap_mask, overlay = self.create_heatmap_overlay(mri_data, saliency_map)
            
            logger.info("Extracting slice visualizations...")
            # Normalize MRI data
            mri_normalized = (mri_data - np.min(mri_data)) / (np.max(mri_data) - np.min(mri_data) + 1e-8)
            
            # Extract slices with bounds checking (use middle slices for safety)
            axial_idx = min(mri_data.shape[2] // 2, mri_data.shape[2] - 1)
            sagittal_idx = min(mri_data.shape[0] // 2, mri_data.shape[0] - 1)
            coronal_idx = min(mri_data.shape[1] // 2, mri_data.shape[1] - 1)
            
            # Axial slice (xy plane)
            visualizations['axial'] = {
                'mri': mri_normalized[:, :, axial_idx],
                'overlay': overlay[:, :, axial_idx] if overlay.ndim == 4 else overlay[:, :, axial_idx],
                'heatmap': heatmap_mask[:, :, axial_idx]
            }
            
            # Sagittal slice (yz plane)
            visualizations['sagittal'] = {
                'mri': mri_normalized[sagittal_idx, :, :],
                'overlay': overlay[sagittal_idx, :, :] if overlay.ndim == 4 else overlay[sagittal_idx, :, :],
                'heatmap': heatmap_mask[sagittal_idx, :, :]
            }
            
            # Coronal slice (xz plane)
            visualizations['coronal'] = {
                'mri': mri_normalized[:, coronal_idx, :],
                'overlay': overlay[:, coronal_idx, :] if overlay.ndim == 4 else overlay[:, coronal_idx, :],
                'heatmap': heatmap_mask[:, coronal_idx, :]
            }
            
            logger.info("Slice visualizations generated successfully")
            return visualizations
        
        except Exception as e:
            logger.error(f"Error generating slice visualizations: {e}")
            # Return fallback visualizations
            mri_normalized = (mri_data - np.min(mri_data)) / (np.max(mri_data) - np.min(mri_data) + 1e-8)
            fallback_viz = {}
            for view, idx in slice_indices.items():
                if view == 'axial':
                    fallback_viz[view] = {
                        'mri': mri_normalized[:, :, min(idx, mri_data.shape[2] - 1)],
                        'overlay': mri_normalized[:, :, min(idx, mri_data.shape[2] - 1)],
                        'heatmap': np.zeros_like(mri_normalized[:, :, min(idx, mri_data.shape[2] - 1)], dtype=bool)
                    }
                elif view == 'sagittal':
                    fallback_viz[view] = {
                        'mri': mri_normalized[min(idx, mri_data.shape[0] - 1), :, :],
                        'overlay': mri_normalized[min(idx, mri_data.shape[0] - 1), :, :],
                        'heatmap': np.zeros_like(mri_normalized[min(idx, mri_data.shape[0] - 1), :, :], dtype=bool)
                    }
                elif view == 'coronal':
                    fallback_viz[view] = {
                        'mri': mri_normalized[:, min(idx, mri_data.shape[1] - 1), :],
                        'overlay': mri_normalized[:, min(idx, mri_data.shape[1] - 1), :],
                        'heatmap': np.zeros_like(mri_normalized[:, min(idx, mri_data.shape[1] - 1), :], dtype=bool)
                    }
            return fallback_viz
    
    def generate_full_explanation(self, mri_data: np.ndarray) -> Dict:
        """
        Generate complete explanation for all classes
        
        Args:
            mri_data: Input MRI data
            
        Returns:
            Dictionary with explanations for all classes
        """
        try:
            results = {}
            
            # Get base prediction
            prediction_results = self.classifier.predict(mri_data)
            results['prediction'] = prediction_results
            
            # Generate explanations for each class
            for class_idx, class_name in enumerate(self.class_labels):
                logger.info(f"Generating explanation for {class_name}...")

                # Preprocess the data first
                processed_data = self.classifier.preprocessor.preprocess_mri(mri_data)

                # Generate gradient explanation
                saliency = self._gradient_explanation(processed_data, class_idx)

                # Generate visualizations using processed data
                visualizations = self.generate_slice_visualizations(processed_data, saliency)

                results[f'{class_name.lower()}_shap_values'] = saliency
                results[f'{class_name.lower()}_visualizations'] = visualizations
            
            return results
            
        except Exception as e:
            logger.error(f"Error generating full explanation: {e}")
            raise


    def generate_explanations(self, mri_data: np.ndarray) -> Dict:
        """
        Generate complete explanations for all three classes

        Args:
            mri_data: Raw MRI data

        Returns:
            Dictionary containing explanations for all classes
        """
        try:
            results = {}

            # Generate explanations for each class
            for class_idx, class_name in enumerate(self.class_labels):
                logger.info(f"Generating explanation for {class_name}...")

                # Preprocess the data first
                processed_data = self.classifier.preprocessor.preprocess_mri(mri_data)

                # Generate gradient explanation
                saliency = self._gradient_explanation(processed_data, class_idx)

                # Generate visualizations using processed data
                visualizations = self.generate_slice_visualizations(processed_data, saliency)

                results[f'{class_name.lower()}_shap_values'] = saliency
                results[f'{class_name.lower()}_visualizations'] = visualizations

            return results

        except Exception as e:
            logger.error(f"Error generating full explanation: {e}")
            raise

def create_shap_explainer(classifier: CNMCIADClassifier,
                         background_samples: Optional[np.ndarray] = None) -> CNMCIADSHAPExplainer:
    """
    Factory function to create SHAP explainer

    Args:
        classifier: CN/MCI/AD classifier instance
        background_samples: Background samples for SHAP

    Returns:
        Initialized SHAP explainer
    """
    return CNMCIADSHAPExplainer(classifier, background_samples)
