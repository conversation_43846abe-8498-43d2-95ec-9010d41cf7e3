"""
Enhanced MRI Preprocessing Pipeline for ncomms2022 Model
Uses FSL pipeline from original ncomms2022 repository for optimal preprocessing
"""

import os
import subprocess
import nibabel as nib
import numpy as np
import tempfile
import shutil
from pathlib import Path
import matplotlib.pyplot as plt
from typing import Union, Tuple, Optional
import logging
from scipy.ndimage import zoom

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NCOMMSFSLPreprocessor:
    """
    MRI Preprocessing pipeline based on ncomms2022 repository FSL pipeline
    Converts T1 scans to preprocessed .npy format using FSL
    """
    
    def __init__(self, fsl_dir: Optional[str] = None):
        """
        Initialize the preprocessor
        
        Args:
            fsl_dir: Path to FSL installation. If None, tries to detect
        """
        self.fsl_dir = fsl_dir or self._detect_fsl()
        self.temp_dir = None
        
        # Check FSL installation
        if not self._check_fsl():
            logger.warning("FSL not found. Will use simplified preprocessing.")
            self.use_fsl = False
        else:
            self.use_fsl = True
    
    def _detect_fsl(self) -> str:
        """Try to detect FSL installation"""
        possible_paths = [
            '/usr/local/fsl',
            '/opt/fsl',
            '/home/<USER>/fsl',
            os.environ.get('FSLDIR', '')
        ]
        
        for path in possible_paths:
            if path and os.path.exists(os.path.join(path, 'bin', 'fslreorient2std')):
                return path
        
        return '/usr/local/fsl'  # Default fallback
    
    def _check_fsl(self) -> bool:
        """Check if FSL is properly installed"""
        try:
            fsl_bin = os.path.join(self.fsl_dir, 'bin', 'fslreorient2std')
            if os.path.exists(fsl_bin):
                return True
            
            # Try system PATH
            result = subprocess.run(['which', 'fslreorient2std'], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False
    
    def _create_temp_dir(self) -> str:
        """Create temporary directory for processing"""
        if self.temp_dir is None:
            self.temp_dir = tempfile.mkdtemp(prefix='ncomms_preprocess_')
        return self.temp_dir
    
    def _cleanup_temp_dir(self):
        """Clean up temporary directory"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            self.temp_dir = None
    
    def _run_fsl_command(self, command: str) -> bool:
        """
        Run FSL command with proper environment
        
        Args:
            command: FSL command to run
            
        Returns:
            True if successful, False otherwise
        """
        try:
            env = os.environ.copy()
            env['FSLDIR'] = self.fsl_dir
            env['FSLOUTPUTTYPE'] = 'NIFTI'
            env['PATH'] = f"{self.fsl_dir}/bin:{env.get('PATH', '')}"
            
            result = subprocess.run(command, shell=True, env=env, 
                                  capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                logger.error(f"FSL command failed: {command}")
                logger.error(f"Error: {result.stderr}")
                return False
            
            return True
        except subprocess.TimeoutExpired:
            logger.error(f"FSL command timed out: {command}")
            return False
        except Exception as e:
            logger.error(f"Error running FSL command: {e}")
            return False
    
    def normalize_intensity(self, data: np.ndarray) -> np.ndarray:
        """
        Normalize intensity values as done in ncomms2022
        
        Args:
            data: Input MRI data
            
        Returns:
            Normalized data
        """
        # Remove background (zero values)
        non_zero_mask = data > 0
        if np.sum(non_zero_mask) == 0:
            logger.warning("All voxels are zero!")
            return data
        
        # Normalize by mean of non-zero voxels and clip to [0, 8] as in original code
        mean_val = np.mean(data[non_zero_mask])
        if mean_val > 0:
            data = data / mean_val
        data = np.clip(data, 0, 8)
        return data
    
    def simple_preprocess(self, input_path: Union[str, Path], output_path: Union[str, Path]) -> bool:
        """
        Simple preprocessing without FSL (fallback method)
        
        Args:
            input_path: Path to input NIfTI file
            output_path: Path for output .npy file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Load image
            img = nib.load(input_path)
            data = img.get_fdata()
            
            # Basic intensity normalization
            normalized_data = self.normalize_intensity(data)
            
            # Resize to target shape
            target_shape = (182, 218, 182)
            if normalized_data.shape != target_shape:
                zoom_factors = [target_shape[i] / normalized_data.shape[i] for i in range(3)]
                normalized_data = zoom(normalized_data, zoom_factors, order=1)
            
            # Save
            np.save(output_path, normalized_data)
            return True
            
        except Exception as e:
            logger.error(f"Simple preprocessing failed: {e}")
            return False
    
    def preprocess_single_scan(self, 
                             input_path: Union[str, Path], 
                             output_path: Optional[Union[str, Path]] = None,
                             step_7_f: float = 0.3,
                             step_7_g: float = 0.0) -> Union[str, None]:
        """
        Preprocess a single T1 MRI scan following ncomms2022 pipeline
        
        Args:
            input_path: Path to input NIfTI file
            output_path: Path for output .npy file. If None, uses input name with .npy extension
            step_7_f: BET fractional intensity threshold (0.3 default)
            step_7_g: BET vertical gradient in fractional intensity threshold (0.0 default)
            
        Returns:
            Path to output .npy file if successful, None otherwise
        """
        input_path = Path(input_path)
        if not input_path.exists():
            logger.error(f"Input file not found: {input_path}")
            return None
        
        if output_path is None:
            output_path = input_path.with_suffix('.npy')
        else:
            output_path = Path(output_path)
        
        # If FSL is not available, use simple preprocessing
        if not self.use_fsl:
            logger.info("Using simple preprocessing (FSL not available)")
            if self.simple_preprocess(input_path, output_path):
                return str(output_path)
            else:
                return None
        
        # Create temporary directory
        temp_dir = self._create_temp_dir()
        
        try:
            # Copy input file to temp directory
            temp_input = os.path.join(temp_dir, 'input.nii')
            shutil.copy2(input_path, temp_input)
            
            logger.info(f"Processing {input_path.name} with FSL pipeline...")
            
            # Step 1: Reorient to standard orientation
            logger.info("Step 1: Reorienting to standard...")
            if not self._run_fsl_command(f"fslreorient2std {temp_input} {temp_dir}/T1.nii"):
                logger.warning("Reorientation failed, using original")
                shutil.copy2(temp_input, f"{temp_dir}/T1.nii")
            
            # Step 2: Robust FOV estimation and cropping
            logger.info("Step 2: Estimating robust FOV...")
            robustfov_result = subprocess.run(f"robustfov -i {temp_dir}/T1.nii", 
                                            shell=True, capture_output=True, text=True)
            
            if robustfov_result.returncode == 0:
                # Parse robustfov output for cropping parameters
                lines = robustfov_result.stdout.strip().split('\n')
                cropped = False
                for line in lines:
                    if not line.startswith('Final') and len(line.split()) >= 6:
                        coords = line.split()
                        try:
                            x1, x2, y1, y2, z1, z2 = [int(float(x)) for x in coords[:6]]
                            crop_cmd = f"fslmaths {temp_dir}/T1.nii -roi {x1} {x2} {y1} {y2} {z1} {z2} 0 1 {temp_dir}/T1_roi.nii"
                            if self._run_fsl_command(crop_cmd):
                                cropped = True
                                break
                        except:
                            continue
                
                if not cropped:
                    shutil.copy2(f"{temp_dir}/T1.nii", f"{temp_dir}/T1_roi.nii")
            else:
                shutil.copy2(f"{temp_dir}/T1.nii", f"{temp_dir}/T1_roi.nii")
            
            # Step 3: Initial skull stripping
            logger.info("Step 3: Initial skull stripping...")
            if not self._run_fsl_command(f"bet {temp_dir}/T1_roi.nii {temp_dir}/T1_brain.nii -R"):
                logger.warning("Initial skull stripping failed, using original")
                shutil.copy2(f"{temp_dir}/T1_roi.nii", f"{temp_dir}/T1_brain.nii")
            
            # Step 4: Registration to MNI space (if template available)
            logger.info("Step 4: Registering to MNI space...")
            mni_template = f"{self.fsl_dir}/data/standard/MNI152_T1_1mm_brain"
            
            if (os.path.exists(f"{mni_template}.nii.gz") or os.path.exists(f"{mni_template}.nii")):
                # Registration
                if self._run_fsl_command(f"flirt -in {temp_dir}/T1_brain.nii -ref {mni_template} -omat {temp_dir}/orig_to_MNI.mat"):
                    # Apply transformation
                    if self._run_fsl_command(f"flirt -in {temp_dir}/T1.nii -ref {mni_template} -applyxfm -init {temp_dir}/orig_to_MNI.mat -out {temp_dir}/T1_MNI.nii"):
                        # Final skull stripping in MNI space
                        logger.info("Step 5: Final skull stripping in MNI space...")
                        if self._run_fsl_command(f"bet {temp_dir}/T1_MNI.nii {temp_dir}/T1_MNI_brain.nii -R -f {step_7_f} -g {step_7_g}"):
                            # Fine-tune registration
                            self._run_fsl_command(f"flirt -in {temp_dir}/T1_MNI_brain.nii -ref {mni_template} -out {temp_dir}/T1_MNI_brain.nii")
                        else:
                            shutil.copy2(f"{temp_dir}/T1_MNI.nii", f"{temp_dir}/T1_MNI_brain.nii")
                    else:
                        shutil.copy2(f"{temp_dir}/T1_brain.nii", f"{temp_dir}/T1_MNI_brain.nii")
                else:
                    shutil.copy2(f"{temp_dir}/T1_brain.nii", f"{temp_dir}/T1_MNI_brain.nii")
            else:
                logger.warning("MNI template not found, skipping registration")
                shutil.copy2(f"{temp_dir}/T1_brain.nii", f"{temp_dir}/T1_MNI_brain.nii")
            
            # Step 6: Convert to numpy and normalize
            logger.info("Step 6: Converting to numpy and normalizing...")
            final_nii = f"{temp_dir}/T1_MNI_brain.nii"
            if not os.path.exists(final_nii):
                logger.error("Final processed file not found")
                return None
            
            # Load and normalize
            img = nib.load(final_nii)
            data = img.get_fdata()
            
            # Normalize intensity
            normalized_data = self.normalize_intensity(data)
            
            # Ensure correct dimensions (182, 218, 182) as expected by ncomms2022
            target_shape = (182, 218, 182)
            if normalized_data.shape != target_shape:
                logger.info(f"Resizing from {normalized_data.shape} to {target_shape}")
                zoom_factors = [target_shape[i] / normalized_data.shape[i] for i in range(3)]
                normalized_data = zoom(normalized_data, zoom_factors, order=1)
            
            # Save as numpy array
            np.save(output_path, normalized_data)
            logger.info(f"Saved preprocessed scan to {output_path}")
            
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error during preprocessing: {e}")
            return None
        
        finally:
            # Clean up temporary directory
            self._cleanup_temp_dir()


def preprocess_mri_scan(input_path: str, output_path: str = None, fsl_dir: str = None) -> str:
    """
    Convenience function to preprocess a single MRI scan
    
    Args:
        input_path: Path to input NIfTI file
        output_path: Path for output .npy file
        fsl_dir: Path to FSL installation
        
    Returns:
        Path to output .npy file if successful, None otherwise
    """
    preprocessor = NCOMMSFSLPreprocessor(fsl_dir=fsl_dir)
    result = preprocessor.preprocess_single_scan(input_path, output_path)
    return result
