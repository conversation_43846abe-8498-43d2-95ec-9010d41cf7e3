"""
MRI Preprocessing Pipeline for CN/MCI/AD 3-Category Classification
Simple preprocessing for memory-efficient CNN model
"""

import os
import numpy as np
import nibabel as nib
import tempfile
from pathlib import Path
from typing import Union, Tuple, Optional
import logging
from scipy.ndimage import zoom

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CNMCIADPreprocessor:
    """
    Preprocessing pipeline for CN/MCI/AD classification
    """
    
    def __init__(self, target_size: Tuple[int, int, int] = (64, 64, 64)):
        """
        Initialize preprocessor
        
        Args:
            target_size: Target size for resizing (default: 64x64x64)
        """
        self.target_size = target_size
        logger.info(f"Initialized CN/MCI/AD preprocessor with target size: {target_size}")
    
    def normalize_intensity(self, data: np.ndarray) -> np.ndarray:
        """
        Normalize intensity values
        
        Args:
            data: Input MRI data
            
        Returns:
            Normalized data
        """
        # Remove outliers (clip to 1st and 99th percentiles)
        p1, p99 = np.percentile(data, [1, 99])
        data = np.clip(data, p1, p99)
        
        # Z-score normalization
        mean = np.mean(data)
        std = np.std(data)
        
        if std > 0:
            normalized = (data - mean) / std
        else:
            normalized = data - mean
        
        return normalized
    
    def resize_volume(self, data: np.ndarray, target_size: Tuple[int, int, int]) -> np.ndarray:
        """
        Resize volume to target size
        
        Args:
            data: Input volume
            target_size: Target dimensions
            
        Returns:
            Resized volume
        """
        current_size = data.shape
        zoom_factors = [target_size[i] / current_size[i] for i in range(3)]
        
        logger.info(f"Resizing from {current_size} to {target_size}")
        resized = zoom(data, zoom_factors, order=1)
        
        return resized
    
    def preprocess_scan(self, input_path: Union[str, Path]) -> Optional[np.ndarray]:
        """
        Preprocess a single MRI scan
        
        Args:
            input_path: Path to input NIfTI file
            
        Returns:
            Preprocessed scan as numpy array, or None if failed
        """
        try:
            input_path = Path(input_path)
            logger.info(f"Preprocessing scan: {input_path.name}")
            
            # Load NIfTI file
            img = nib.load(input_path)
            data = img.get_fdata()
            
            logger.info(f"Original shape: {data.shape}")
            
            # Ensure 3D
            if data.ndim == 4:
                # Take first volume if 4D
                data = data[:, :, :, 0]
                logger.info("Converted 4D to 3D by taking first volume")
            
            # Resize to target size
            if data.shape != self.target_size:
                data = self.resize_volume(data, self.target_size)
            
            # Normalize intensity
            data = self.normalize_intensity(data)
            
            logger.info(f"Final shape: {data.shape}")
            logger.info(f"Intensity range: [{np.min(data):.3f}, {np.max(data):.3f}]")
            
            return data
            
        except Exception as e:
            logger.error(f"Error preprocessing {input_path}: {e}")
            return None
    
    def preprocess_from_bytes(self, file_bytes: bytes, filename: str) -> Optional[np.ndarray]:
        """
        Preprocess MRI scan from bytes (for Streamlit file upload)
        
        Args:
            file_bytes: File content as bytes
            filename: Original filename
            
        Returns:
            Preprocessed scan as numpy array, or None if failed
        """
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.nii') as tmp_file:
                tmp_file.write(file_bytes)
                tmp_file.flush()
                
                # Process the temporary file
                result = self.preprocess_scan(tmp_file.name)
                
                # Clean up
                os.unlink(tmp_file.name)
                
                return result
                
        except Exception as e:
            logger.error(f"Error preprocessing {filename}: {e}")
            return None
    
    def batch_preprocess(self, input_dir: Union[str, Path], 
                        output_dir: Union[str, Path]) -> int:
        """
        Preprocess multiple scans in a directory
        
        Args:
            input_dir: Directory containing input NIfTI files
            output_dir: Directory to save preprocessed files
            
        Returns:
            Number of successfully processed files
        """
        input_dir = Path(input_dir)
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Find all NIfTI files
        nii_files = list(input_dir.glob("*.nii")) + list(input_dir.glob("*.nii.gz"))
        
        logger.info(f"Found {len(nii_files)} NIfTI files to process")
        
        processed_count = 0
        
        for nii_file in nii_files:
            try:
                # Preprocess
                processed_data = self.preprocess_scan(nii_file)
                
                if processed_data is not None:
                    # Save as numpy array
                    output_path = output_dir / f"{nii_file.stem}_preprocessed.npy"
                    np.save(output_path, processed_data)
                    logger.info(f"Saved: {output_path}")
                    processed_count += 1
                else:
                    logger.warning(f"Failed to process: {nii_file}")
                    
            except Exception as e:
                logger.error(f"Error processing {nii_file}: {e}")
        
        logger.info(f"Successfully processed {processed_count}/{len(nii_files)} files")
        return processed_count
    
    def get_preprocessing_info(self) -> dict:
        """
        Get information about preprocessing parameters
        
        Returns:
            Dictionary with preprocessing information
        """
        return {
            'target_size': self.target_size,
            'normalization': 'Z-score normalization',
            'outlier_handling': 'Clip to 1st-99th percentiles',
            'resampling': 'Linear interpolation (order=1)',
            'model_compatibility': 'Memory-Efficient CNN for CN/MCI/AD'
        }


def create_preprocessor(target_size: Tuple[int, int, int] = (64, 64, 64)) -> CNMCIADPreprocessor:
    """
    Factory function to create preprocessor
    
    Args:
        target_size: Target size for preprocessing
        
    Returns:
        Initialized preprocessor
    """
    return CNMCIADPreprocessor(target_size)


# Test function
def test_preprocessor():
    """Test the preprocessor with a dummy volume"""
    logger.info("Testing CN/MCI/AD preprocessor...")
    
    # Create dummy data
    dummy_data = np.random.randn(128, 128, 128) * 100 + 500
    
    # Save as temporary NIfTI
    with tempfile.NamedTemporaryFile(suffix='.nii', delete=False) as tmp_file:
        img = nib.Nifti1Image(dummy_data, np.eye(4))
        nib.save(img, tmp_file.name)
        
        # Test preprocessing
        preprocessor = create_preprocessor()
        result = preprocessor.preprocess_scan(tmp_file.name)
        
        # Clean up
        os.unlink(tmp_file.name)
        
        if result is not None:
            logger.info(f"✅ Test passed! Output shape: {result.shape}")
            logger.info(f"   Intensity range: [{np.min(result):.3f}, {np.max(result):.3f}]")
            return True
        else:
            logger.error("❌ Test failed!")
            return False


if __name__ == "__main__":
    test_preprocessor()
