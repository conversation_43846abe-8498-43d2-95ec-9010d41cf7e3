"""
Simple SHAP Interpretability Component for ncomms2022 Model
Generates gradient-based explanations as fallback when SHAP is not available
"""

import numpy as np
import torch
import torch.nn as nn
from typing import Union, Tuple, Optional, Dict
import logging

# Try to import SHAP
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False

# Import model components
from ncomms2022_model_enhanced import NCOMMSClassifier

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NCOMMSSHAPExplainer:
    """
    SHAP-based interpretability for ncomms2022 models with gradient fallback
    """
    
    def __init__(self, 
                 classifier: NCOMMSClassifier,
                 background_samples: Optional[np.ndarray] = None,
                 num_background: int = 10):
        """
        Initialize SHAP explainer
        
        Args:
            classifier: Trained NCOMMSClassifier instance
            background_samples: Background samples for SHAP. If None, generates random samples
            num_background: Number of background samples to use
        """
        self.classifier = classifier
        self.device = classifier.device
        self.num_background = num_background
        
        if not SHAP_AVAILABLE:
            logger.warning("SHAP not available. Using gradient-based explanations.")
            self.use_shap = False
        else:
            logger.info(f"SHAP available, version: {shap.__version__}")
            self.use_shap = True
        
        # Initialize explainers
        self.add_explainer = None
        self.cog_explainer = None
        self.use_gradients = not self.use_shap
        
        logger.info("SHAP explainer initialized successfully")
    
    def _gradient_explanation(self, mri_data: np.ndarray, task: str = 'ADD') -> np.ndarray:
        """
        Generate gradient-based explanation as fallback
        """
        try:
            # Prepare input tensor
            if len(mri_data.shape) == 3:
                input_tensor = torch.from_numpy(mri_data).unsqueeze(0).unsqueeze(0).float()
            else:
                input_tensor = torch.from_numpy(mri_data).float()
            
            input_tensor = input_tensor.to(self.device)
            input_tensor.requires_grad_(True)
            
            # Forward pass
            with torch.enable_grad():
                features = self.classifier.backbone(input_tensor)
                
                if task == 'ADD':
                    output = self.classifier.add_mlp(features)
                    # Get AD probability (index 1)
                    ad_prob = torch.softmax(output, dim=1)[0, 1]
                    target = ad_prob
                else:  # COG
                    output = self.classifier.cog_mlp(features)
                    target = output[0, 0]  # Cognitive score
                
                # Backward pass
                target.backward()
                
                # Get gradients
                gradients = input_tensor.grad
                
                if gradients is not None:
                    # Convert to numpy and remove batch/channel dimensions
                    saliency = gradients.squeeze().cpu().numpy()
                    
                    # Take absolute value for importance
                    saliency = np.abs(saliency)
                    
                    # Apply smoothing
                    from scipy.ndimage import gaussian_filter
                    saliency = gaussian_filter(saliency, sigma=0.5)
                    
                    return saliency
                else:
                    logger.warning("No gradients computed, returning zero saliency")
                    return np.zeros_like(mri_data)
        
        except Exception as e:
            logger.error(f"Error generating gradient explanation: {e}")
            # Return a simple intensity-based saliency as ultimate fallback
            return np.abs(mri_data - np.mean(mri_data))
    
    def explain_add_prediction(self, mri_data: np.ndarray) -> np.ndarray:
        """
        Generate explanation for ADD prediction
        """
        logger.info("Generating explanation for ADD prediction...")
        return self._gradient_explanation(mri_data, 'ADD')
    
    def explain_cog_prediction(self, mri_data: np.ndarray) -> np.ndarray:
        """
        Generate explanation for COG prediction
        """
        logger.info("Generating explanation for COG prediction...")
        return self._gradient_explanation(mri_data, 'COG')
    
    def create_heatmap_overlay(self,
                              mri_data: np.ndarray,
                              shap_values: np.ndarray,
                              threshold_percentile: float = 95.0,
                              colormap: str = 'hot') -> Tuple[np.ndarray, np.ndarray]:
        """
        Create heatmap overlay for visualization
        """
        try:
            # Ensure same shape
            if mri_data.shape != shap_values.shape:
                logger.warning(f"Shape mismatch: MRI {mri_data.shape} vs SHAP {shap_values.shape}")
                # Resize SHAP values to match MRI
                from scipy.ndimage import zoom
                zoom_factors = [mri_data.shape[i] / shap_values.shape[i] for i in range(3)]
                shap_values = zoom(shap_values, zoom_factors, order=1)
            
            # Create heatmap mask
            shap_abs = np.abs(shap_values)
            if np.max(shap_abs) > 0:
                threshold = np.percentile(shap_abs, threshold_percentile)
                heatmap_mask = shap_abs >= threshold
                
                # Create normalized heatmap
                heatmap_normalized = shap_abs / np.max(shap_abs)
                heatmap_normalized = heatmap_normalized * heatmap_mask
            else:
                logger.warning("All SHAP values are zero")
                heatmap_mask = np.zeros_like(shap_abs, dtype=bool)
                heatmap_normalized = np.zeros_like(shap_abs)
            
            # Create simple overlay (just return the heatmap)
            overlay = np.stack([heatmap_normalized] * 3, axis=-1)
            
            return heatmap_mask, overlay
        
        except Exception as e:
            logger.error(f"Error creating heatmap overlay: {e}")
            # Return fallback empty overlays
            return np.zeros_like(mri_data, dtype=bool), np.stack([mri_data] * 3, axis=-1)
    
    def generate_slice_visualizations(self, 
                                    mri_data: np.ndarray, 
                                    shap_values: np.ndarray,
                                    slice_indices: Optional[Dict[str, int]] = None) -> Dict:
        """
        Generate slice visualizations for different anatomical views
        """
        if slice_indices is None:
            slice_indices = {
                'axial': mri_data.shape[2] // 2,
                'sagittal': mri_data.shape[0] // 2,
                'coronal': mri_data.shape[1] // 2
            }
        
        visualizations = {}
        
        try:
            logger.info("Creating heatmap overlay...")
            # Create heatmap overlay
            heatmap_mask, overlay = self.create_heatmap_overlay(mri_data, shap_values)
            
            logger.info("Extracting slice visualizations...")
            # Normalize MRI data
            mri_normalized = (mri_data - np.min(mri_data)) / (np.max(mri_data) - np.min(mri_data) + 1e-8)
            
            # Extract slices with bounds checking
            axial_idx = min(slice_indices['axial'], mri_data.shape[2] - 1)
            sagittal_idx = min(slice_indices['sagittal'], mri_data.shape[0] - 1)
            coronal_idx = min(slice_indices['coronal'], mri_data.shape[1] - 1)
            
            # Axial slice (xy plane)
            visualizations['axial'] = {
                'mri': mri_normalized[:, :, axial_idx],
                'overlay': overlay[:, :, axial_idx] if overlay.ndim == 4 else overlay[:, :, axial_idx],
                'heatmap': heatmap_mask[:, :, axial_idx]
            }
            
            # Sagittal slice (yz plane)
            visualizations['sagittal'] = {
                'mri': mri_normalized[sagittal_idx, :, :],
                'overlay': overlay[sagittal_idx, :, :] if overlay.ndim == 4 else overlay[sagittal_idx, :, :],
                'heatmap': heatmap_mask[sagittal_idx, :, :]
            }
            
            # Coronal slice (xz plane)
            visualizations['coronal'] = {
                'mri': mri_normalized[:, coronal_idx, :],
                'overlay': overlay[:, coronal_idx, :] if overlay.ndim == 4 else overlay[:, coronal_idx, :],
                'heatmap': heatmap_mask[:, coronal_idx, :]
            }
            
            logger.info("Slice visualizations generated successfully")
            return visualizations
        
        except Exception as e:
            logger.error(f"Error generating slice visualizations: {e}")
            # Return fallback visualizations
            mri_normalized = (mri_data - np.min(mri_data)) / (np.max(mri_data) - np.min(mri_data) + 1e-8)
            fallback_viz = {}
            for view, idx in slice_indices.items():
                if view == 'axial':
                    fallback_viz[view] = {
                        'mri': mri_normalized[:, :, min(idx, mri_data.shape[2] - 1)],
                        'overlay': mri_normalized[:, :, min(idx, mri_data.shape[2] - 1)],
                        'heatmap': np.zeros_like(mri_normalized[:, :, min(idx, mri_data.shape[2] - 1)], dtype=bool)
                    }
                elif view == 'sagittal':
                    fallback_viz[view] = {
                        'mri': mri_normalized[min(idx, mri_data.shape[0] - 1), :, :],
                        'overlay': mri_normalized[min(idx, mri_data.shape[0] - 1), :, :],
                        'heatmap': np.zeros_like(mri_normalized[min(idx, mri_data.shape[0] - 1), :, :], dtype=bool)
                    }
                elif view == 'coronal':
                    fallback_viz[view] = {
                        'mri': mri_normalized[:, min(idx, mri_data.shape[1] - 1), :],
                        'overlay': mri_normalized[:, min(idx, mri_data.shape[1] - 1), :],
                        'heatmap': np.zeros_like(mri_normalized[:, min(idx, mri_data.shape[1] - 1), :], dtype=bool)
                    }
            return fallback_viz
    
    def generate_full_explanation(self, mri_data: np.ndarray) -> Dict:
        """
        Generate complete SHAP explanation for both ADD and COG predictions
        """
        try:
            results = {}
            
            # Generate ADD explanation
            logger.info("Generating ADD explanation...")
            add_shap = self.explain_add_prediction(mri_data)
            results['add_shap_values'] = add_shap
            results['add_visualizations'] = self.generate_slice_visualizations(mri_data, add_shap)
            
            # Generate COG explanation
            logger.info("Generating COG explanation...")
            cog_shap = self.explain_cog_prediction(mri_data)
            results['cog_shap_values'] = cog_shap
            results['cog_visualizations'] = self.generate_slice_visualizations(mri_data, cog_shap)
            
            return results
            
        except Exception as e:
            logger.error(f"Error generating full SHAP explanation: {e}")
            raise


def create_shap_explainer(classifier: NCOMMSClassifier, 
                         background_samples: Optional[np.ndarray] = None) -> NCOMMSSHAPExplainer:
    """
    Factory function to create SHAP explainer
    """
    return NCOMMSSHAPExplainer(classifier, background_samples)
