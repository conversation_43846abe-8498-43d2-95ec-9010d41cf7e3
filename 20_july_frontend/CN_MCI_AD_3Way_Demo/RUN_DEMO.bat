@echo off
setlocal enabledelayedexpansion

title Demetify CN/MCI/AD 3-Way Demo - Professional Launcher

REM ========================================
REM  DEMETIFY CN/MCI/AD 3-WAY DEMO LAUNCHER
REM ========================================

cd /d "%~dp0"

echo ========================================
echo  DEMETIFY CN/MCI/AD 3-WAY DEMO
echo ========================================
echo.
echo Advanced 3-Category Dementia Assessment System
echo University of Illinois at Urbana-Champaign
echo.
echo Working directory: %CD%
echo Current time: %TIME%
echo.

set "ERROR_OCCURRED=0"

REM ========================================
REM  SYSTEM VERIFICATION
REM ========================================

echo [1/6] Verifying demo files...

REM Verify we're in the right directory
if not exist "cn_mci_ad_frontend.py" (
    echo ERROR: Demo files not found in current directory
    echo Expected location: %CD%
    echo Missing file: cn_mci_ad_frontend.py
    echo.
    echo SOLUTION: Ensure all demo files are in the same folder
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

if not exist "cn_mci_ad_model.py" (
    echo ERROR: cn_mci_ad_model.py not found
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

if not exist "requirements.txt" (
    echo ERROR: requirements.txt not found
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

echo ✅ All demo files found

REM ========================================
REM  PYTHON DETECTION
REM ========================================

echo.
echo [2/6] Detecting Python installation...

set "PYTHON_CMD="
for %%p in (python python3 py) do (
    %%p --version >nul 2>&1
    if !errorlevel! equ 0 (
        set "PYTHON_CMD=%%p"
        goto :python_found
    )
)

echo ❌ ERROR: Python not found
echo.
echo SOLUTION: Install Python from https://python.org
echo Make sure to check "Add Python to PATH" during installation
set "ERROR_OCCURRED=1"
goto :error_exit

:python_found
echo ✅ Python found: !PYTHON_CMD!

REM Get Python version
for /f "tokens=2" %%v in ('!PYTHON_CMD! --version 2^>^&1') do set "PYTHON_VERSION=%%v"
echo Python version: !PYTHON_VERSION!

REM ========================================
REM  DEPENDENCY INSTALLATION
REM ========================================

echo.
echo [3/6] Installing/updating dependencies...
echo This may take a few minutes on first run...

!PYTHON_CMD! -m pip install -r requirements.txt --user --quiet
set "PIP_RESULT=!errorlevel!"

if !PIP_RESULT! neq 0 (
    echo ⚠️ WARNING: Some dependencies may have failed to install
    echo Continuing anyway - many packages might already be installed
) else (
    echo ✅ Dependencies installed successfully
)

REM ========================================
REM  DEPENDENCY VERIFICATION
REM ========================================

echo.
echo [4/6] Verifying critical packages...

set "MISSING_PACKAGES=0"

!PYTHON_CMD! -c "import streamlit" >nul 2>&1
if !errorlevel! neq 0 (
    echo ❌ Streamlit not available
    set "MISSING_PACKAGES=1"
) else (
    echo ✅ Streamlit
)

!PYTHON_CMD! -c "import torch" >nul 2>&1
if !errorlevel! neq 0 (
    echo ❌ PyTorch not available
    set "MISSING_PACKAGES=1"
) else (
    echo ✅ PyTorch
)

!PYTHON_CMD! -c "import numpy" >nul 2>&1
if !errorlevel! neq 0 (
    echo ❌ NumPy not available
    set "MISSING_PACKAGES=1"
) else (
    echo ✅ NumPy
)

!PYTHON_CMD! -c "import nibabel" >nul 2>&1
if !errorlevel! neq 0 (
    echo ❌ NiBabel not available
    set "MISSING_PACKAGES=1"
) else (
    echo ✅ NiBabel
)

if !MISSING_PACKAGES! equ 1 (
    echo.
    echo ⚠️ WARNING: Some critical packages are missing
    echo The demo may not work properly
    echo.
    echo SOLUTION: Try running INSTALL_PYTHON_AND_DEPENDENCIES.bat
    echo.
    choice /c YN /m "Continue anyway? (Y/N)"
    if !errorlevel! equ 2 goto :error_exit
)

REM ========================================
REM  MODEL VERIFICATION
REM ========================================

echo.
echo [5/6] Verifying model components...

!PYTHON_CMD! -c "
import sys
sys.path.append('.')
try:
    from cn_mci_ad_model import CNMCIADClassifier
    print('✅ Model components verified')
except Exception as e:
    print('❌ Model verification failed:', e)
    sys.exit(1)
" 2>nul

if !errorlevel! neq 0 (
    echo ⚠️ WARNING: Model verification failed
    echo The demo will run in simulation mode
)

REM ========================================
REM  LAUNCH DEMO
REM ========================================

echo.
echo [6/6] Starting Demetify CN/MCI/AD Demo...
echo.
echo ========================================
echo  STARTING DEMO SERVER
echo ========================================
echo.
echo The demo will open in your browser shortly...
echo.
echo Features:
echo • 3-Category Classification (CN/MCI/AD)
echo • Real-time MRI Analysis
echo • SHAP Interpretability Maps
echo • Professional Interface
echo.

REM Start Streamlit server
echo Command: !PYTHON_CMD! -m streamlit run cn_mci_ad_frontend.py
echo.

start "Demetify CN/MCI/AD Demo" !PYTHON_CMD! -m streamlit run cn_mci_ad_frontend.py --server.port 8504 --server.address localhost --browser.gatherUsageStats false

REM Wait for server to start
echo Waiting for server to initialize...
timeout /t 5 /nobreak >nul

REM Try to open browser
echo Opening browser...
start http://localhost:8504

echo.
echo ========================================
echo  DEMO IS RUNNING
echo ========================================
echo.
echo 🌐 Demo URL: http://localhost:8504
echo 📁 Upload MRI scans (.nii or .nii.gz files)
echo 🧠 Get CN/MCI/AD classification results
echo 🔍 View SHAP interpretability maps
echo.
echo Press Ctrl+C in the server window to stop the demo
echo.
echo Press any key to exit this launcher...
pause >nul

goto :end

REM ========================================
REM  ERROR HANDLING
REM ========================================

:error_exit
echo.
echo ========================================
echo  SETUP FAILED
echo ========================================
echo.
echo The demo could not be started due to the errors above.
echo.
echo TROUBLESHOOTING STEPS:
echo.
echo 1. INSTALL PYTHON:
echo    Download from: https://python.org
echo    ✓ Check "Add Python to PATH"
echo    ✓ Use Python 3.8 or newer
echo.
echo 2. INSTALL DEPENDENCIES:
echo    Run: INSTALL_PYTHON_AND_DEPENDENCIES.bat
echo    Or manually: pip install -r requirements.txt
echo.
echo 3. VERIFY FILES:
echo    Ensure all demo files are in: %CD%
echo    Required: cn_mci_ad_frontend.py, cn_mci_ad_model.py, requirements.txt
echo.
echo 4. MANUAL START:
echo    python -m streamlit run cn_mci_ad_frontend.py --server.port 8503
echo.
echo 5. GET HELP:
echo    Contact: Demetify Development Team
echo    University of Illinois at Urbana-Champaign
echo.
echo Press any key to exit...
pause >nul
exit /b 1

:end
echo.
echo Thank you for using Demetify CN/MCI/AD Demo!
echo University of Illinois at Urbana-Champaign
exit /b 0
