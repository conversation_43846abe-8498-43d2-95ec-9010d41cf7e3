"""
SHAP Interpretability System for CN/MCI/AD Classification
Provides gradient-based explanations with heatmap overlays for all three classes
"""

import numpy as np
import torch
import torch.nn as nn
from typing import Dict, Tuple, Optional
import logging
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from scipy.ndimage import gaussian_filter

# Try to import SHAP
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False

# Import model components
from cn_mci_ad_model import CNMCIADClassifier

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CNMCIADSHAPExplainer:
    """
    SHAP-based interpretability for CN/MCI/AD classification
    Uses gradient-based explanations as fallback when SHAP is not available
    """
    
    def __init__(self, classifier: CNMCIADClassifier):
        """
        Initialize SHAP explainer
        
        Args:
            classifier: Trained CNMCIADClassifier instance
        """
        self.classifier = classifier
        self.device = classifier.device
        self.class_labels = classifier.class_labels
        
        if not SHAP_AVAILABLE:
            logger.warning("SHAP not available. Using gradient-based explanations.")
            self.use_shap = False
        else:
            logger.info(f"SHAP available, version: {shap.__version__}")
            self.use_shap = True
        
        logger.info("SHAP explainer initialized successfully")
    
    def _gradient_explanation(self, mri_data: np.ndarray, target_class: int) -> np.ndarray:
        """
        Generate high-quality gradient-based explanation for a specific class

        Args:
            mri_data: Preprocessed MRI data
            target_class: Target class index (0=CN, 1=MCI, 2=AD)

        Returns:
            Enhanced gradient-based saliency map
        """
        try:
            # Prepare input tensor
            input_tensor = torch.from_numpy(mri_data).float()
            input_tensor = input_tensor.unsqueeze(0).unsqueeze(0)  # Add batch and channel dims
            input_tensor = input_tensor.to(self.device)
            input_tensor.requires_grad_(True)

            # Forward pass
            with torch.enable_grad():
                logits = self.classifier.model(input_tensor)

                # Get target class score (use softmax for better gradients)
                probabilities = torch.softmax(logits, dim=1)
                target_score = probabilities[0, target_class]

                # Backward pass
                target_score.backward()

                # Get gradients
                gradients = input_tensor.grad

                if gradients is not None:
                    # Convert to numpy and remove batch/channel dimensions
                    saliency = gradients.squeeze().cpu().numpy()

                    # Enhanced gradient processing
                    # 1. Use guided backpropagation concept (keep positive gradients)
                    saliency = np.maximum(saliency, 0)

                    # 2. Apply input multiplication for better localization
                    saliency = saliency * np.abs(mri_data)

                    # 3. Apply multi-scale smoothing
                    saliency_smooth = np.zeros_like(saliency)
                    for sigma in [0.5, 1.0, 1.5]:
                        weight = 1.0 / sigma
                        saliency_smooth += weight * gaussian_filter(saliency, sigma=sigma)

                    saliency = saliency_smooth

                    # 4. Enhance contrast using percentile-based normalization
                    if np.max(saliency) > 0:
                        # Use 95th percentile for better contrast
                        p95 = np.percentile(saliency, 95)
                        if p95 > 0:
                            saliency = np.clip(saliency / p95, 0, 1)
                        else:
                            saliency = saliency / np.max(saliency)

                    # 5. Apply threshold to focus on most important regions
                    threshold = np.percentile(saliency, 75)
                    saliency = np.where(saliency >= threshold, saliency, saliency * 0.3)

                    return saliency
                else:
                    logger.warning("No gradients computed, returning zero saliency")
                    return np.zeros_like(mri_data)

        except Exception as e:
            logger.error(f"Error generating gradient explanation: {e}")
            # Enhanced fallback using image statistics
            fallback = np.abs(mri_data - np.mean(mri_data))
            fallback = gaussian_filter(fallback, sigma=1.0)
            if np.max(fallback) > 0:
                fallback = fallback / np.max(fallback)
            return fallback
    
    def explain_class_prediction(self, mri_data: np.ndarray, class_name: str) -> np.ndarray:
        """
        Generate explanation for a specific class prediction
        
        Args:
            mri_data: Raw MRI data
            class_name: Class name ('CN', 'MCI', or 'AD')
            
        Returns:
            SHAP/gradient values for the specified class
        """
        try:
            # Get class index
            class_idx = self.class_labels.index(class_name)
            
            # Preprocess the data
            processed_data = self.classifier.preprocessor.preprocess_mri(mri_data)
            
            logger.info(f"Generating explanation for {class_name} prediction...")
            
            # Generate gradient-based explanation
            explanation = self._gradient_explanation(processed_data, class_idx)
            
            return explanation
            
        except Exception as e:
            logger.error(f"Error generating {class_name} explanation: {e}")
            # Return fallback explanation
            processed_data = self.classifier.preprocessor.preprocess_mri(mri_data)
            return np.abs(processed_data - np.mean(processed_data))
    
    def create_heatmap_overlay(self,
                              mri_data: np.ndarray,
                              explanation_values: np.ndarray,
                              threshold_percentile: float = 90.0) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create heatmap overlay for visualization
        
        Args:
            mri_data: Original MRI data
            explanation_values: SHAP/gradient values
            threshold_percentile: Percentile threshold for heatmap
            
        Returns:
            Tuple of (heatmap_mask, overlay_image)
        """
        try:
            # Ensure same shape
            if mri_data.shape != explanation_values.shape:
                logger.warning(f"Shape mismatch: MRI {mri_data.shape} vs explanation {explanation_values.shape}")
                # Use the explanation values shape as reference
                from scipy.ndimage import zoom
                zoom_factors = [explanation_values.shape[i] / mri_data.shape[i] for i in range(3)]
                mri_data = zoom(mri_data, zoom_factors, order=1)
            
            # Create heatmap mask
            explanation_abs = np.abs(explanation_values)
            if np.max(explanation_abs) > 0:
                threshold = np.percentile(explanation_abs, threshold_percentile)
                heatmap_mask = explanation_abs >= threshold
                
                # Create normalized heatmap
                heatmap_normalized = explanation_abs / np.max(explanation_abs)
                heatmap_normalized = heatmap_normalized * heatmap_mask
            else:
                logger.warning("All explanation values are zero")
                heatmap_mask = np.zeros_like(explanation_abs, dtype=bool)
                heatmap_normalized = np.zeros_like(explanation_abs)
            
            # Create overlay image
            overlay = self._create_overlay_image(mri_data, heatmap_normalized)
            
            return heatmap_mask, overlay
        
        except Exception as e:
            logger.error(f"Error creating heatmap overlay: {e}")
            # Return fallback empty overlays
            return np.zeros_like(mri_data, dtype=bool), np.stack([mri_data] * 3, axis=-1)
    
    def _create_overlay_image(self, mri_data: np.ndarray, heatmap: np.ndarray, colormap: str = 'hot') -> np.ndarray:
        """
        Create high-quality RGB overlay image combining MRI and heatmap
        """
        # Normalize MRI data to [0, 1]
        mri_normalized = (mri_data - np.min(mri_data)) / (np.max(mri_data) - np.min(mri_data) + 1e-8)

        # Create RGB image
        overlay = np.zeros((*mri_data.shape, 3))

        # Set grayscale MRI as base
        overlay[:, :, :, 0] = mri_normalized
        overlay[:, :, :, 1] = mri_normalized
        overlay[:, :, :, 2] = mri_normalized

        # Create colormap for heatmap
        if colormap == 'hot':
            # Hot colormap: black -> red -> yellow -> white
            heatmap_rgb = np.zeros((*heatmap.shape, 3))

            # Red channel: increases from 0 to 1
            heatmap_rgb[:, :, :, 0] = heatmap

            # Green channel: increases after red reaches 0.5
            green_mask = heatmap > 0.5
            heatmap_rgb[:, :, :, 1] = np.where(green_mask, (heatmap - 0.5) * 2, 0)

            # Blue channel: increases after green reaches 0.5 (i.e., heatmap > 0.75)
            blue_mask = heatmap > 0.75
            heatmap_rgb[:, :, :, 2] = np.where(blue_mask, (heatmap - 0.75) * 4, 0)

        elif colormap == 'jet':
            # Jet colormap approximation
            heatmap_rgb = np.zeros((*heatmap.shape, 3))

            # Blue to cyan (0 to 0.25)
            blue_mask = heatmap <= 0.25
            heatmap_rgb[:, :, :, 2] = np.where(blue_mask, 1, 0)
            heatmap_rgb[:, :, :, 1] = np.where(blue_mask, heatmap * 4, 0)

            # Cyan to green (0.25 to 0.5)
            cyan_mask = (heatmap > 0.25) & (heatmap <= 0.5)
            heatmap_rgb[:, :, :, 1] = np.where(cyan_mask, 1, heatmap_rgb[:, :, :, 1])
            heatmap_rgb[:, :, :, 2] = np.where(cyan_mask, 1 - (heatmap - 0.25) * 4, heatmap_rgb[:, :, :, 2])

            # Green to yellow (0.5 to 0.75)
            green_mask = (heatmap > 0.5) & (heatmap <= 0.75)
            heatmap_rgb[:, :, :, 1] = np.where(green_mask, 1, heatmap_rgb[:, :, :, 1])
            heatmap_rgb[:, :, :, 0] = np.where(green_mask, (heatmap - 0.5) * 4, 0)

            # Yellow to red (0.75 to 1)
            yellow_mask = heatmap > 0.75
            heatmap_rgb[:, :, :, 0] = np.where(yellow_mask, 1, heatmap_rgb[:, :, :, 0])
            heatmap_rgb[:, :, :, 1] = np.where(yellow_mask, 1 - (heatmap - 0.75) * 4, heatmap_rgb[:, :, :, 1])

        else:  # Default to red
            heatmap_rgb = np.zeros((*heatmap.shape, 3))
            heatmap_rgb[:, :, :, 0] = heatmap

        # Blend heatmap with MRI using alpha blending
        alpha = 0.6  # Heatmap transparency
        heatmap_mask = heatmap > 0.1  # Only show significant activations

        for i in range(3):
            overlay[:, :, :, i] = np.where(
                heatmap_mask,
                (1 - alpha) * overlay[:, :, :, i] + alpha * heatmap_rgb[:, :, :, i],
                overlay[:, :, :, i]
            )

        # Ensure values are in [0, 1]
        overlay = np.clip(overlay, 0, 1)

        return overlay
    
    def generate_slice_visualizations(self, 
                                    mri_data: np.ndarray, 
                                    explanation_values: np.ndarray,
                                    slice_indices: Optional[Dict[str, int]] = None) -> Dict:
        """
        Generate slice visualizations for different anatomical views
        
        Args:
            mri_data: Original MRI data
            explanation_values: SHAP/gradient values
            slice_indices: Dictionary specifying slice indices for each view
            
        Returns:
            Dictionary containing visualization data for each view
        """
        if slice_indices is None:
            slice_indices = {
                'axial': mri_data.shape[2] // 2,
                'sagittal': mri_data.shape[0] // 2,
                'coronal': mri_data.shape[1] // 2
            }
        
        visualizations = {}
        
        try:
            logger.info("Creating heatmap overlay...")
            # Create heatmap overlay
            heatmap_mask, overlay = self.create_heatmap_overlay(mri_data, explanation_values)
            
            logger.info("Extracting slice visualizations...")
            # Normalize MRI data
            mri_normalized = (mri_data - np.min(mri_data)) / (np.max(mri_data) - np.min(mri_data) + 1e-8)
            
            # Extract slices with bounds checking
            axial_idx = min(slice_indices['axial'], mri_data.shape[2] - 1)
            sagittal_idx = min(slice_indices['sagittal'], mri_data.shape[0] - 1)
            coronal_idx = min(slice_indices['coronal'], mri_data.shape[1] - 1)
            
            # Axial slice (xy plane)
            visualizations['axial'] = {
                'mri': mri_normalized[:, :, axial_idx],
                'overlay': overlay[:, :, axial_idx] if overlay.ndim == 4 else overlay[:, :, axial_idx],
                'heatmap': heatmap_mask[:, :, axial_idx],
                'explanation': explanation_values[:, :, axial_idx]
            }
            
            # Sagittal slice (yz plane)
            visualizations['sagittal'] = {
                'mri': mri_normalized[sagittal_idx, :, :],
                'overlay': overlay[sagittal_idx, :, :] if overlay.ndim == 4 else overlay[sagittal_idx, :, :],
                'heatmap': heatmap_mask[sagittal_idx, :, :],
                'explanation': explanation_values[sagittal_idx, :, :]
            }
            
            # Coronal slice (xz plane)
            visualizations['coronal'] = {
                'mri': mri_normalized[:, coronal_idx, :],
                'overlay': overlay[:, coronal_idx, :] if overlay.ndim == 4 else overlay[:, coronal_idx, :],
                'heatmap': heatmap_mask[:, coronal_idx, :],
                'explanation': explanation_values[:, coronal_idx, :]
            }
            
            logger.info("Slice visualizations generated successfully")
            return visualizations
        
        except Exception as e:
            logger.error(f"Error generating slice visualizations: {e}")
            # Return fallback visualizations
            mri_normalized = (mri_data - np.min(mri_data)) / (np.max(mri_data) - np.min(mri_data) + 1e-8)
            fallback_viz = {}
            for view, idx in slice_indices.items():
                if view == 'axial':
                    fallback_viz[view] = {
                        'mri': mri_normalized[:, :, min(idx, mri_data.shape[2] - 1)],
                        'overlay': mri_normalized[:, :, min(idx, mri_data.shape[2] - 1)],
                        'heatmap': np.zeros_like(mri_normalized[:, :, min(idx, mri_data.shape[2] - 1)], dtype=bool),
                        'explanation': np.zeros_like(mri_normalized[:, :, min(idx, mri_data.shape[2] - 1)])
                    }
                elif view == 'sagittal':
                    fallback_viz[view] = {
                        'mri': mri_normalized[min(idx, mri_data.shape[0] - 1), :, :],
                        'overlay': mri_normalized[min(idx, mri_data.shape[0] - 1), :, :],
                        'heatmap': np.zeros_like(mri_normalized[min(idx, mri_data.shape[0] - 1), :, :], dtype=bool),
                        'explanation': np.zeros_like(mri_normalized[min(idx, mri_data.shape[0] - 1), :, :])
                    }
                elif view == 'coronal':
                    fallback_viz[view] = {
                        'mri': mri_normalized[:, min(idx, mri_data.shape[1] - 1), :],
                        'overlay': mri_normalized[:, min(idx, mri_data.shape[1] - 1), :],
                        'heatmap': np.zeros_like(mri_normalized[:, min(idx, mri_data.shape[1] - 1), :], dtype=bool),
                        'explanation': np.zeros_like(mri_normalized[:, min(idx, mri_data.shape[1] - 1), :])
                    }
            return fallback_viz
    
    def generate_explanations(self, mri_data: np.ndarray) -> Dict:
        """
        Generate complete explanations for all three classes
        
        Args:
            mri_data: Raw MRI data
            
        Returns:
            Dictionary containing explanations for all classes
        """
        try:
            results = {}
            
            # Generate explanations for each class
            for class_name in self.class_labels:
                logger.info(f"Generating {class_name} explanation...")
                explanation = self.explain_class_prediction(mri_data, class_name)
                results[f'{class_name.lower()}_shap_values'] = explanation
                results[f'{class_name.lower()}_visualizations'] = self.generate_slice_visualizations(
                    mri_data, explanation
                )
            
            return results
            
        except Exception as e:
            logger.error(f"Error generating full explanations: {e}")
            raise

def create_shap_explainer(classifier: CNMCIADClassifier) -> CNMCIADSHAPExplainer:
    """Factory function to create SHAP explainer"""
    return CNMCIADSHAPExplainer(classifier)
