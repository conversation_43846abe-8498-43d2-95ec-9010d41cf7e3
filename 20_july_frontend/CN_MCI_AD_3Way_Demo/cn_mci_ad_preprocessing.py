"""
Enhanced MRI Preprocessing Pipeline for CN/MCI/AD Classification
Adapted from ncomms2022 FSL pipeline for 3-category classification
"""

import os
import subprocess
import nibabel as nib
import numpy as np
import tempfile
import shutil
from pathlib import Path
import matplotlib.pyplot as plt
from typing import Union, Tuple, Optional
import logging
from scipy.ndimage import zoom

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CNMCIADPreprocessor:
    """
    MRI Preprocessing pipeline for CN/MCI/AD classification
    Uses FSL-based preprocessing with fallback to simplified processing
    """
    
    def __init__(self, target_shape: Tuple[int, int, int] = (64, 64, 64), fsl_dir: Optional[str] = None):
        """
        Initialize the preprocessor
        
        Args:
            target_shape: Target shape for resized volumes
            fsl_dir: Path to FSL installation. If None, tries to detect
        """
        self.target_shape = target_shape
        self.fsl_dir = fsl_dir or self._detect_fsl()
        self.temp_dir = None
        
        # Check FSL installation
        if not self._check_fsl():
            logger.warning("FSL not found. Will use simplified preprocessing.")
            self.use_fsl = False
        else:
            logger.info("FSL detected. Using FSL-based preprocessing.")
            self.use_fsl = True
    
    def _detect_fsl(self) -> str:
        """Try to detect FSL installation"""
        possible_paths = [
            '/usr/local/fsl',
            '/opt/fsl',
            '/home/<USER>/fsl',
            os.environ.get('FSLDIR', ''),
            '/usr/share/fsl'
        ]
        
        for path in possible_paths:
            if path and Path(path).exists():
                return path
        
        return ""
    
    def _check_fsl(self) -> bool:
        """Check if FSL is available"""
        try:
            result = subprocess.run(['which', 'fslreorient2std'], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False
    
    def _run_fsl_command(self, command: str) -> bool:
        """Run FSL command with error handling"""
        try:
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            if result.returncode != 0:
                logger.warning(f"FSL command failed: {command}")
                logger.warning(f"Error: {result.stderr}")
                return False
            return True
        except Exception as e:
            logger.error(f"Error running FSL command: {e}")
            return False
    
    def normalize_intensity(self, data: np.ndarray) -> np.ndarray:
        """
        Normalize intensity values using robust statistics
        """
        # Remove outliers (clip to 1st and 99th percentiles)
        p1, p99 = np.percentile(data, [1, 99])
        data_clipped = np.clip(data, p1, p99)
        
        # Z-score normalization
        mean_val = np.mean(data_clipped)
        std_val = np.std(data_clipped)
        
        if std_val > 0:
            normalized = (data_clipped - mean_val) / std_val
        else:
            logger.warning("Standard deviation is zero, using min-max normalization")
            min_val, max_val = np.min(data_clipped), np.max(data_clipped)
            if max_val > min_val:
                normalized = (data_clipped - min_val) / (max_val - min_val)
            else:
                normalized = data_clipped
        
        return normalized.astype(np.float32)
    
    def preprocess_with_fsl(self, input_path: Path, output_path: Path) -> Optional[str]:
        """
        Preprocess MRI using FSL pipeline
        """
        temp_dir = tempfile.mkdtemp(prefix='cn_mci_ad_preprocessing_')
        
        try:
            # Copy input file to temp directory
            temp_input = os.path.join(temp_dir, 'input.nii')
            shutil.copy2(input_path, temp_input)
            
            logger.info(f"Processing {input_path.name} with FSL pipeline...")
            
            # Step 1: Reorient to standard orientation
            logger.info("Step 1: Reorienting to standard...")
            if not self._run_fsl_command(f"fslreorient2std {temp_input} {temp_dir}/T1.nii"):
                logger.warning("Reorientation failed, using original")
                shutil.copy2(temp_input, f"{temp_dir}/T1.nii")
            
            # Step 2: Robust FOV estimation and cropping
            logger.info("Step 2: Estimating robust FOV...")
            robustfov_result = subprocess.run(f"robustfov -i {temp_dir}/T1.nii", 
                                            shell=True, capture_output=True, text=True)
            
            if robustfov_result.returncode == 0:
                # Extract transformation matrix from robustfov output
                for line in robustfov_result.stdout.split('\n'):
                    if 'fslroi' in line:
                        roi_command = line.strip()
                        if roi_command:
                            logger.info(f"Applying ROI: {roi_command}")
                            self._run_fsl_command(f"{roi_command.replace('$FSLDIR/bin/', '')}")
                            break
            
            # Step 3: Brain extraction
            logger.info("Step 3: Brain extraction...")
            if not self._run_fsl_command(f"bet {temp_dir}/T1.nii {temp_dir}/T1_brain.nii -R -f 0.5 -g 0"):
                logger.warning("Brain extraction failed, using original")
                shutil.copy2(f"{temp_dir}/T1.nii", f"{temp_dir}/T1_brain.nii")
            
            # Step 4: Bias field correction
            logger.info("Step 4: Bias field correction...")
            if not self._run_fsl_command(f"fast -t 1 -n 3 -H 0.1 -I 4 -l 20.0 -B -o {temp_dir}/T1_brain {temp_dir}/T1_brain.nii"):
                logger.warning("Bias correction failed, using brain extracted image")
                shutil.copy2(f"{temp_dir}/T1_brain.nii", f"{temp_dir}/T1_brain_restore.nii")
            
            # Use the bias-corrected image if available
            final_nii = f"{temp_dir}/T1_brain_restore.nii"
            if not os.path.exists(final_nii):
                final_nii = f"{temp_dir}/T1_brain.nii"
            if not os.path.exists(final_nii):
                final_nii = f"{temp_dir}/T1.nii"
            
            # Load and normalize
            img = nib.load(final_nii)
            data = img.get_fdata()
            
            # Normalize intensity
            normalized_data = self.normalize_intensity(data)
            
            # Resize to target shape
            if normalized_data.shape != self.target_shape:
                logger.info(f"Resizing from {normalized_data.shape} to {self.target_shape}")
                zoom_factors = [self.target_shape[i] / normalized_data.shape[i] for i in range(3)]
                normalized_data = zoom(normalized_data, zoom_factors, order=1)
            
            # Save as numpy array
            np.save(output_path, normalized_data)
            logger.info(f"Saved preprocessed scan to {output_path}")
            
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error during FSL preprocessing: {e}")
            return None
        finally:
            # Cleanup
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
    
    def preprocess_simplified(self, input_path: Path, output_path: Path) -> Optional[str]:
        """
        Simplified preprocessing without FSL
        """
        try:
            logger.info(f"Using simplified preprocessing for {input_path.name}")
            
            # Load image
            img = nib.load(input_path)
            data = img.get_fdata()
            
            # Handle 4D data
            if len(data.shape) == 4:
                data = data[:, :, :, 0]
            
            # Basic skull stripping (remove low-intensity background)
            threshold = np.percentile(data[data > 0], 10)
            mask = data > threshold
            data = data * mask
            
            # Normalize intensity
            normalized_data = self.normalize_intensity(data)
            
            # Resize to target shape
            if normalized_data.shape != self.target_shape:
                logger.info(f"Resizing from {normalized_data.shape} to {self.target_shape}")
                zoom_factors = [self.target_shape[i] / normalized_data.shape[i] for i in range(3)]
                normalized_data = zoom(normalized_data, zoom_factors, order=1)
            
            # Save as numpy array
            np.save(output_path, normalized_data)
            logger.info(f"Saved preprocessed scan to {output_path}")
            
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error during simplified preprocessing: {e}")
            return None
    
    def preprocess_mri(self, mri_data: Union[np.ndarray, str, Path]) -> np.ndarray:
        """
        Main preprocessing function
        
        Args:
            mri_data: Either numpy array or path to NIfTI file
            
        Returns:
            Preprocessed MRI data as numpy array
        """
        try:
            if isinstance(mri_data, (str, Path)):
                # File path provided
                input_path = Path(mri_data)
                
                with tempfile.NamedTemporaryFile(suffix='.npy', delete=False) as tmp_file:
                    output_path = tmp_file.name
                
                # Try FSL preprocessing first
                if self.use_fsl:
                    result_path = self.preprocess_with_fsl(input_path, output_path)
                else:
                    result_path = self.preprocess_simplified(input_path, output_path)
                
                if result_path:
                    processed_data = np.load(result_path)
                    os.unlink(result_path)  # Cleanup
                    return processed_data
                else:
                    raise Exception("Preprocessing failed")
            
            else:
                # Numpy array provided - apply basic preprocessing
                logger.info(f"Preprocessing numpy array with shape: {mri_data.shape}")
                
                # Handle different input shapes
                if len(mri_data.shape) == 4:
                    mri_data = mri_data[:, :, :, 0]
                
                # Resize to target shape
                if mri_data.shape != self.target_shape:
                    logger.info(f"Resizing from {mri_data.shape} to {self.target_shape}")
                    zoom_factors = [self.target_shape[i] / mri_data.shape[i] for i in range(3)]
                    mri_data = zoom(mri_data, zoom_factors, order=1)
                
                # Normalize intensity
                normalized_data = self.normalize_intensity(mri_data)
                
                logger.info(f"Preprocessing complete. Final shape: {normalized_data.shape}")
                logger.info(f"Data range: [{np.min(normalized_data):.3f}, {np.max(normalized_data):.3f}]")
                
                return normalized_data
                
        except Exception as e:
            logger.error(f"Error in preprocessing: {e}")
            raise

def create_preprocessor(target_shape: Tuple[int, int, int] = (64, 64, 64)) -> CNMCIADPreprocessor:
    """Factory function to create preprocessor"""
    return CNMCIADPreprocessor(target_shape=target_shape)
