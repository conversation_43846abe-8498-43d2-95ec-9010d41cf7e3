@echo off
setlocal enabledelayedexpansion

title Demetify CN/MCI/AD Demo - Quick Start

echo ========================================
echo  DEMETIFY CN/MCI/AD DEMO - QUICK START
echo ========================================
echo.

cd /d "%~dp0"

REM Quick file check
if not exist "cn_mci_ad_frontend.py" (
    echo ❌ ERROR: cn_mci_ad_frontend.py not found
    echo Current directory: %CD%
    pause
    exit /b 1
)

REM Find Python
set "PYTHON_CMD="
for %%p in (python python3 py) do (
    %%p --version >nul 2>&1
    if !errorlevel! equ 0 (
        set "PYTHON_CMD=%%p"
        goto :found_python
    )
)

echo ❌ ERROR: Python not found
echo Please install Python from https://python.org
pause
exit /b 1

:found_python
echo ✅ Found Python: !PYTHON_CMD!

REM Quick dependency install
echo Installing dependencies...
!PYTHON_CMD! -m pip install -r requirements.txt --user --quiet

REM Start demo
echo.
echo Starting CN/MCI/AD Demo...
echo Demo will open at: http://localhost:8503
echo.

start "Demetify Demo" !PYTHON_CMD! -m streamlit run cn_mci_ad_frontend.py --server.port 8503 --server.address localhost --browser.gatherUsageStats false

REM Wait and open browser
timeout /t 3 /nobreak >nul
start http://localhost:8503

echo.
echo Demo is running! Press any key to exit...
pause >nul
